<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.ApprovalMapper">
    <resultMap id="ApprovalResult" type="com.zte.uedm.dcdigital.infrastructure.repository.po.ApprovalPo">
        <!-- 主键字段 -->
        <id property="id" column="id"/>

        <!-- 基础字段 -->
        <result property="title" column="title"/>
        <result property="approvalType" column="approval_type"/>
        <result property="submitUser" column="submit_user"/>
        <result property="submitTime" column="submit_time"/>
        <result property="flowId" column="flow_id"/>
        <result property="resourceId" column="resource_id"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="bussesDataJson" column="busses_data_json"/>
        <result property="billQuantityId" column="bill_quantity_id"/>
        <result property="billQuantityPerson" column="bill_quantity_person"/>
        <result property="handlerTime" column="handler_time"/>
        <result property="projectId" column="project_id"/>
    </resultMap>
    <select id="queryByFlowId" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ApprovalPo">
        select * from approval where flow_id = #{flowId}
    </select>

    <select id="queryPendingList" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ApprovalPo">
        select * from approval
        where 1=1
        and status in (0, 1)
        <if test="queryDto.title != null and queryDto.title != ''">
            and title LIKE CONCAT('%', #{queryDto.title}, '%')
        </if>
        <if test="queryDto.type != null">
            and approval_type = #{queryDto.type}
        </if>
        <if test="queryDto.startTime != null and queryDto.startTime != ''">
            and submit_time &gt;= #{queryDto.startTime}
        </if>
        <if test="queryDto.endTime != null and queryDto.endTime != ''">
            and submit_time &lt;= #{queryDto.endTime}
        </if>
        <if test="userIds != null and userIds.size() > 0">
            and submit_user in (
            <foreach collection="userIds" item="userId" separator=",">
                #{userId}
            </foreach>
            )
        </if>
        order by submit_time
        <choose>
            <when test="queryDto.order != '' and queryDto.order == 'asc'">
                asc
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </select>

<!--    <select id="queryInitiatedList"-->
<!--            resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ApprovalPo">-->
<!--        select * from approval-->
<!--        where 1=1-->
<!--        <if test="queryDto.title != null and queryDto.title != ''">-->
<!--            and title LIKE CONCAT('%', #{queryDto.title}, '%')-->
<!--        </if>-->
<!--        <if test="queryDto.type != null">-->
<!--            and approval_type = #{queryDto.type}-->
<!--        </if>-->
<!--        <if test="queryDto.status != null and queryDto.status == 2">-->
<!--            and status in (2,3)-->
<!--        </if>-->
<!--        <if test="queryDto.status != null and queryDto.status != 2">-->
<!--            and status = #{queryDto.status}-->
<!--        </if>-->
<!--        <if test="queryDto.startTime != null and queryDto.startTime != ''">-->
<!--            and submit_time &gt;= #{queryDto.startTime}-->
<!--        </if>-->
<!--        <if test="queryDto.endTime != null and queryDto.endTime != ''">-->
<!--            and submit_time &lt;= #{queryDto.endTime}-->
<!--        </if>-->
<!--        <if test="userIds != null and userIds.size() > 0">-->
<!--            and submit_user in (-->
<!--            <foreach collection="userIds" item="userId" separator=",">-->
<!--                #{userId}-->
<!--            </foreach>-->
<!--            )-->
<!--        </if>-->
<!--        order by submit_time-->
<!--        <choose>-->
<!--            <when test="queryDto.order != '' and queryDto.order == 'asc'">-->
<!--                asc-->
<!--            </when>-->
<!--            <otherwise>-->
<!--                desc-->
<!--            </otherwise>-->
<!--        </choose>-->
<!--    </select>-->

    <!-- Started by AICoder, pid:maa7dy965d36e491406109b480dc554fffc4ce92 -->
    <select id="queryPendingListNew" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ApprovalPo"
            parameterType="com.zte.uedm.dcdigital.interfaces.web.dto.ProcessPendingDto">
        <!-- 查询待处理审批列表 -->
        select * from approval
        where 1=1
        and approval_type != 4
        <!-- 过滤条件：流程实例ID集合 -->
        and flow_id in (
        <foreach collection="procInsIds" item="flowId" separator=",">
            #{flowId}
        </foreach>
        )
        <!-- 过滤条件：标题模糊匹配 -->
        <if test="title != null and title != ''">
            and title LIKE CONCAT('%', #{title}, '%')
        </if>
        <!-- 过滤条件：审批类型 -->
        <if test="approvalType != null">
            and approval_type = #{approvalType}
        </if>
        <!-- 过滤条件：提交时间起始 -->
        <if test="startTime != null and startTime != ''">
            and submit_time &gt;= #{startTime}
        </if>
        <!-- 过滤条件：提交时间结束 -->
        <if test="endTime != null and endTime != ''">
            and submit_time &lt;= #{endTime}
        </if>
        <!-- 过滤条件：提交用户 -->
        <if test="userId != null and userId != ''">
            and submit_user = #{userId}
        </if>
        <!-- 排序条件：按提交时间排序 -->
        order by submit_time
        <choose>
            <!-- 如果排序方式为升序，则使用 asc -->
            <when test="order != '' and order == 'asc'">
                asc
            </when>
            <!-- 否则默认降序 -->
            <otherwise>
                desc
            </otherwise>
        </choose>
    </select>
    <!-- Ended by AICoder, pid:maa7dy965d36e491406109b480dc554fffc4ce92 -->

    <!-- 查询"待我处理"  下面的sql有些复杂最主要的目的都是针对approval_type为5、6、7、9这3种类型的流程只能展示最新的数据，原sql查询语句注释如上-->
    <select id="queryInitiatedList" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ApprovalPo">
        WITH FilteredData AS (
        SELECT
        *,
        CASE WHEN approval_type IN (5,6,7,9) THEN
        ROW_NUMBER() OVER (
        PARTITION BY approval_type, resource_id, project_id
        ORDER BY submit_time DESC
        )
        ELSE 1 END AS rn
        FROM approval
        WHERE 1=1 and approval_type != 4
        <if test="queryDto.title != null and queryDto.title != ''">
            AND title LIKE CONCAT('%', #{queryDto.title}, '%')
        </if>
        <if test="queryDto.type != null">
            AND approval_type = #{queryDto.type}
        </if>
        <if test="queryDto.status != null and queryDto.status == 2">
            AND status IN (2,3)
        </if>
        <if test="queryDto.status != null and queryDto.status != 2">
            AND status = #{queryDto.status}
        </if>
        <if test="queryDto.startTime != null and queryDto.startTime != ''">
            AND submit_time &gt;= #{queryDto.startTime}
        </if>
        <if test="queryDto.endTime != null and queryDto.endTime != ''">
            AND submit_time &lt;= #{queryDto.endTime}
        </if>
        <if test="userIds != null and userIds.size() > 0">
            AND submit_user IN (
            <foreach collection="userIds" item="userId" separator=",">
                #{userId}
            </foreach>
            )
        </if>
        )
        SELECT * FROM FilteredData
        WHERE
        (approval_type IN (5,6,7,9) AND rn = 1)
        OR approval_type NOT IN (5,6,7,9)
        ORDER BY
        <choose>
            <when test="queryDto.order != '' and queryDto.order == 'asc'">
                submit_time ASC
            </when>
            <otherwise>
                submit_time DESC
            </otherwise>
        </choose>
    </select>

<!--    <select id="queryCompletedList"-->
<!--            resultType="com.zte.uedm.dcdigital.domain.model.approval.ApprovalWithResultObj">-->
<!--        select-->
<!--        a.id,-->
<!--        a.title,-->
<!--        a.approval_type,-->
<!--        a.submit_user,-->
<!--        a.submit_time,-->
<!--        a.flow_id,-->
<!--        a.resource_id,-->
<!--        a.status,-->
<!--        a.create_time,-->
<!--        a.update_time,-->
<!--        a.create_by,-->
<!--        a.update_by,-->
<!--        a.busses_data_json,-->
<!--        ac.result,-->
<!--        ac.static_task_id-->
<!--        from (-->
<!--        SELECT a1.*-->
<!--        FROM approval_comment a1-->
<!--        INNER JOIN (-->
<!--        SELECT flow_id, MAX(create_time) AS max_time-->
<!--        FROM approval_comment where user_id = #{userId}-->
<!--        GROUP BY flow_id-->
<!--        ) b-->
<!--        ON a1.flow_id = b.flow_id AND a1.create_time = b.max_time-->
<!--        ) ac-->
<!--        left join approval a on a.id = ac.approval_id-->
<!--        where 1=1-->
<!--        <if test="queryDto.title != null and queryDto.title != ''">-->
<!--            AND a.title LIKE CONCAT('%', #{queryDto.title}, '%')-->
<!--        </if>-->
<!--        <if test="queryDto.type != null">-->
<!--            and a.approval_type = #{queryDto.type}-->
<!--        </if>-->
<!--        <if test="queryDto.result != null">-->
<!--            and ac.result = #{queryDto.result}-->
<!--        </if>-->
<!--        <if test="queryDto.startTime != null and queryDto.startTime != ''">-->
<!--            and a.submit_time &gt;= #{queryDto.startTime}-->
<!--        </if>-->
<!--        <if test="queryDto.endTime != null and queryDto.endTime != ''">-->
<!--            and a.submit_time &lt;= #{queryDto.endTime}-->
<!--        </if>-->
<!--        <if test="userIds != null and userIds.size() > 0">-->
<!--            and a.submit_user in (-->
<!--            <foreach collection="userIds" item="id" separator=",">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--            )-->
<!--        </if>-->
<!--        order by submit_time-->
<!--        <choose>-->
<!--            <when test="queryDto.order != '' and queryDto.order == 'asc'">-->
<!--                asc-->
<!--            </when>-->
<!--            <otherwise>-->
<!--                desc-->
<!--            </otherwise>-->
<!--        </choose>-->
<!--    </select>-->

<!-- 查询"我已处理"  下面的sql有些复杂最主要的目的都是针对approval_type为5、6、7这3种类型的流程只能展示最新的数据，原sql查询语句注释如上-->
    <select id="queryCompletedList" resultType="com.zte.uedm.dcdigital.domain.model.approval.ApprovalWithResultObj">
        SELECT
        a.id,
        a.title,
        a.approval_type,
        a.submit_user,
        a.submit_time,
        a.flow_id,
        a.resource_id,
        a.status,
        a.create_time,
        a.update_time,
        a.create_by,
        a.update_by,
        a.busses_data_json,
        a.project_id,
        a.handler_time,
        ac.result,
        ac.static_task_id
        FROM (
        SELECT
        ac.*,
        a.approval_type,  <!-- 在子查询中关联approval表并获取approval_type -->
        a.resource_id,
        a.project_id,
        ROW_NUMBER() OVER (
        PARTITION BY
        CASE
        WHEN a.approval_type IN (5,6,7,9)  <!-- 使用approval表的字段 -->
        THEN CONCAT(a.approval_type, '_', a.resource_id, '_', a.project_id)
        ELSE ac.flow_id
        END
        ORDER BY ac.create_time DESC
        ) AS rn
        FROM approval_comment ac
        INNER JOIN approval a ON a.id = ac.approval_id  <!-- 提前关联approval表 -->
        WHERE ac.user_id = #{userId}
        ) ac
        INNER JOIN approval a ON a.id = ac.approval_id  <!-- 再次关联确保字段可用 -->
        WHERE ac.rn = 1
        AND 1=1 and a.approval_type != 4
        <!-- 条件过滤（与之前相同） -->
        <if test="queryDto.title != null and queryDto.title != ''">
            AND a.title LIKE CONCAT('%', #{queryDto.title}, '%')
        </if>
        <if test="queryDto.type != null">
            AND a.approval_type = #{queryDto.type}
        </if>
        <if test="queryDto.result != null">
            AND ac.result = #{queryDto.result}
        </if>
        <if test="queryDto.startTime != null and queryDto.startTime != ''">
            AND a.submit_time &gt;= #{queryDto.startTime}
        </if>
        <if test="queryDto.endTime != null and queryDto.endTime != ''">
            AND a.submit_time &lt;= #{queryDto.endTime}
        </if>
        <if test="userIds != null and userIds.size() > 0">
            AND a.submit_user IN
            <foreach collection="userIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <!-- 排序和分页（与之前相同） -->
        ORDER BY a.submit_time
        <choose>
            <when test="queryDto.order != '' and queryDto.order == 'asc'">
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>
    </select>

    <select id="queryByFlowIds" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ApprovalPo">
        select * from approval
        <where>
            <if test="flowIds != null and flowIds.size() > 0">
                flow_id in
                <foreach collection="flowIds" item="flowId" open="(" close=")" separator=",">
                    #{flowId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="calculateTasks" resultType="com.zte.uedm.dcdigital.interfaces.web.vo.TaskDetailVo">
        select a.resource_id as productSubcategory,count(a.resource_id) as mainTotal
        from approval a where a.busses_data_json = #{bussesDataJson}
        and a.approval_type = 4 and a.status != 4
        GROUP BY a.resource_id
    </select>

    <select id="calculateByProjectId" resultType="com.zte.uedm.dcdigital.interfaces.web.vo.TaskDetailVo">
        select a.resource_id as productSubcategory,count(a.resource_id) as mainTotal
        from approval a where a.project_id = #{projectId} and a.approval_type != 4
        GROUP BY a.resource_id
    </select>

    <update id="updateBussesDataJson">
        update approval set busses_data_json = #{data} where flow_id = #{flowId}
    </update>

    <update id="updateFlowIdById">
        update approval set flow_id = #{flowId} where id = #{id}
    </update>

    <select id="queryByMaterialId" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ApprovalPo">
        select * from approval where busses_data_json CONCAT('%', #{materialId}, '%') and status in (0,1) limit 1
    </select>
    <select id="queryApprovalByProjectIdAndType"
            resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ApprovalPo">
        SELECT * FROM (
        SELECT
        t.*,
        CASE WHEN t.approval_type IN (5,6,7,9) THEN
        ROW_NUMBER() OVER (
        PARTITION BY t.approval_type, t.resource_id, t.project_id
        ORDER BY t.submit_time DESC
        )
        ELSE 1 END AS rn
        FROM (
        SELECT *
        FROM approval
        WHERE project_id = #{projectId}
        AND approval_type = #{type}
        ) t
        ) final_data
        WHERE
        (approval_type IN (5,6,7,9) AND rn = 1)
        OR approval_type NOT IN (5,6,7,9)
        ORDER BY submit_time DESC
    </select>
    <select id="queryTaskStatisticsListByApprovalTypeList" resultMap="ApprovalResult">
        SELECT
        id,
        title,
        approval_type,
        submit_user,
        submit_time,
        flow_id,
        resource_id,
        status,
        create_time,
        update_time,
        create_by,
        update_by,
        busses_data_json,
        bill_quantity_id,
        bill_quantity_person,
        handler_time,
        project_id
        FROM public.approval
        <where>
            approval_type IN
            <foreach item="type" collection="approvalTypeList" open="(" separator="," close=")">
                #{type}
            </foreach>
        </where>
    </select>
</mapper>