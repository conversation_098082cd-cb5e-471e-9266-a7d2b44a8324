package com.zte.uedm.dcdigital.application.approval.executor.impl;

import com.zte.uedm.dcdigital.application.approval.executor.InnerApprovalCommandService;
import com.zte.uedm.dcdigital.common.bean.process.TaskDataInnerVo;
import com.zte.uedm.dcdigital.common.bean.project.LaunchBiddingInnerVo;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.domain.model.process.ApprovalCommentEntity;
import com.zte.uedm.dcdigital.domain.model.process.ApprovalEntity;
import com.zte.uedm.dcdigital.domain.repository.ApprovalCommentRepository;
import com.zte.uedm.dcdigital.domain.repository.ApprovalRepository;
import com.zte.uedm.dcdigital.domain.utils.DateUtils;
import com.zte.uedm.dcdigital.sdk.project.service.DeepenDesignInnerService;
import com.zte.uedm.dcdigital.sdk.project.service.ProjectService;
import com.zte.uedm.dcdigital.sdk.project.vo.DeepenImplementationInnerVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class InnerApprovalCommandServiceImpl implements InnerApprovalCommandService {

    // 定义要查询的任务类型
    private static final List<Integer> approvalTypeList = Arrays.asList(5, 6, 7, 8, 9);

    // 定义任务类型(5, 6, 7, 9)的"完成"节点标识
    private static final int COMPLETE_NODE = 12;
    // 定义任务类型(8)的"完成"节点标识
    private static final int COMPLETE_NODE_TENDER = 21;
    @Autowired
    private ApprovalRepository approvalRepository;

    @Autowired
    private ApprovalCommentRepository commentRepository;
    @Autowired
    private DeepenDesignInnerService designInnerService;

    @Autowired
    private ProjectService projectService;

    @Override
    public List<TaskDataInnerVo> queryTaskStatistics() {


        // 查询审批数据
        List<ApprovalEntity> approvalEntities = approvalRepository.queryTaskStatisticsListByApprovalTypeList(approvalTypeList);

        // 存放最终结果
        Map<String, ApprovalEntity> resultMap = new HashMap<>();

        // 存放类型为8的审批数据（招标申请任务不需要去重）
        List<ApprovalEntity> tenderList = new ArrayList<>();

        // 处理需要去重的数据（5,6,7,9）
        for (ApprovalEntity entity : approvalEntities) {
            Integer type = entity.getApprovalType();
            if (type == 8) {
                tenderList.add(entity);
            } else if (type == 5 || type == 6 || type == 7 || type == 9) {
                String key = buildKey(entity);
                ApprovalEntity existing = resultMap.get(key);

                // 如果不存在或者当前实体的提交时间 更 新，则替换为存储最新时间的流程记录
                if (existing == null || isLater(entity.getSubmitTime(), existing.getSubmitTime())) {
                    resultMap.put(key, entity);
                }
            }
        }

        // 合并所有待用于统计的任务数据
        List<ApprovalEntity> finalList = new ArrayList<>(resultMap.values());
        finalList.addAll(tenderList);


        // 构造返回值
        List<TaskDataInnerVo> voList = new ArrayList<>();

        calculateDelayedTaskNum(finalList, voList);

        return voList;
    }

    //统计每个产品小品类的任务个数
    private void calculateDelayedTaskNum(List<ApprovalEntity> finalList, List<TaskDataInnerVo> voList) {
        //1：按产品小类分组
        Map<String, List<ApprovalEntity>> tasksByCategory = finalList.stream().collect(Collectors.groupingBy(ApprovalEntity::getResourceId));

        //2：获取所有任务的flowId集合
        List<String> flowIds = finalList.stream().map(ApprovalEntity::getFlowId).collect(Collectors.toList());

        //2.1:查询所有任务的实际完成时间（标书澄清、方案制作、文档编写、产品提资和招标任务）
        List<ApprovalCommentEntity> commentEntities = queryApprovalCommentList(flowIds);
        //(标书澄清、方案制作、文档编写、产品提资)任务节点实际完成时间
        Map<String, String> completionTimeMap = queryCompletionTime(commentEntities);
        //(招标申请)任务节点实际完成时间
        Map<String, String> tenderCompletionTimeMap = tenderQueryCompletionTime(commentEntities);
        completionTimeMap.putAll(tenderCompletionTimeMap); // 合并所有任务的实际完成时间(key为flowId,value为具体时间,为null或空就说明该节点还未执行)

        //2.2:查询所有任务的规定完成时间（标书澄清、方案制作、文档编写、产品提资和招标任务）
        // 标书澄清、方案制作、文档编写、招标任务（类型 5,6,7,8）的规定完成时间
        Map<String, List<LaunchBiddingInnerVo>> normalDueTimeMap = allocateCompletionTime(finalList);
        // 提资任务（类型 9）的规定完成时间
        Map<String, List<DeepenImplementationInnerVo>> upgradeDueTimeMap = allocateUpgradeCompletionTime(finalList);

        //当前日期(年-月-日)
        String currentDate = DateTimeUtils.getCurrentDate();
        // 统计每个产品小类的任务状态
        for (Map.Entry<String, List<ApprovalEntity>> entry : tasksByCategory.entrySet()) {
            String resourceId = entry.getKey();
            List<ApprovalEntity> tasks = entry.getValue();

            TaskDataInnerVo vo = new TaskDataInnerVo();
            vo.setProductCategoryId(resourceId);
            vo.setAllNum(tasks.size());
            int delayedCount = 0;
            int onTimeCount = 0;

            for (ApprovalEntity task : tasks) {
                // 实际完成时间
                String actualTime = completionTimeMap.get(task.getFlowId());

                // 规定完成时间
                String dueTime = getDueTime(task, normalDueTimeMap, upgradeDueTimeMap);

                // 判断是否延期
                boolean isDelayed = DateUtils.isDelayed(actualTime, dueTime, currentDate);

                if (isDelayed) {
                    delayedCount++;
                } else {
                    onTimeCount++;
                }
            }


            //当前产品小类的未延迟任务个数
            vo.setUnPostponedNum(onTimeCount);
            //当前产品小类的延迟任务个数
            vo.setPostponedNum(delayedCount);
            voList.add(vo);
        }

    }
        /*
        ✅ 情况一：实际完成时间 存在（非 null）
        如果 实际完成时间 <= 规定完成时间 → 未延期
        如果 实际完成时间 > 规定完成时间 → 延期

        ✅ 情况二：实际完成时间 不存在（null 或空）
        如果 规定完成时间 < 当前时间 → 延期
        如果 规定完成时间 >= 当前时间 → 未延期
        */

    //根据flowIds列表合集查询approval_comment表查询每个任务在"完成"节点的处理时间(如果对应节点没有值,就说明任务流程还没有到达该节点)
    private List<ApprovalCommentEntity> queryApprovalCommentList(List<String> flowIds) {
        List<ApprovalCommentEntity> approvalCommentEntities = commentRepository.queryApprovalCommentListByFlowIds(flowIds);
        return approvalCommentEntities;


    }

    //(标书澄清、方案制作、文档编写、产品提资)任务节点实际完成时间
    private Map<String, String> queryCompletionTime(List<ApprovalCommentEntity> approvalCommentEntities) {
        Map<String, String> completionTimeMap = approvalCommentEntities.stream().filter(e -> e.getResult() == COMPLETE_NODE) // 只保留 result == 12 的记录
                .collect(Collectors.toMap(ApprovalCommentEntity::getFlowId, e -> DateUtils.formatToShortDate(e.getCreateTime()), // 使用工具类
                        (existing, replacement) -> existing));
        return completionTimeMap;
    }

    //(招标申请)任务节点实际完成时间
    private Map<String, String> tenderQueryCompletionTime(List<ApprovalCommentEntity> approvalCommentEntities) {
        Map<String, String> tenderCompletionTimeMap = approvalCommentEntities.stream().filter(e -> e.getResult() == COMPLETE_NODE_TENDER) // 只保留 result == 21 的记录
                .collect(Collectors.toMap(ApprovalCommentEntity::getFlowId, e -> DateUtils.formatToShortDate(e.getCreateTime()), // 使用工具类
                        (existing, replacement) -> existing));
        return tenderCompletionTimeMap;
    }

    //标书澄清、方案制作、文档编写、招标申请的规定完成时间(approvalType为5,6,7,8)
    private Map<String, List<LaunchBiddingInnerVo>> allocateCompletionTime(List<ApprovalEntity> finalList) {
        //收集approvalType为5,6,7,8的任务商机projectIds集合
        List<String> projectIds = finalList.stream().filter(entity -> entity.getApprovalType() == 5 || entity.getApprovalType() == 6 || entity.getApprovalType() == 7 || entity.getApprovalType() == 8).map(ApprovalEntity::getProjectId).distinct().collect(Collectors.toList());
        List<LaunchBiddingInnerVo> biddingInnerVoList = queryLaunchBiddingList(projectIds);
        return biddingInnerVoList.stream().collect(Collectors.groupingBy(LaunchBiddingInnerVo::getId));

    }

    /**
     * 辅助方法：获取任务的规定完成时间
     */

    /**
     * 主入口方法（圈复杂度=5）
     */
    private String getDueTime(ApprovalEntity task,
                              Map<String, List<LaunchBiddingInnerVo>> normalDueTimeMap,
                              Map<String, List<DeepenImplementationInnerVo>> upgradeDueTimeMap) {
        if (task == null || task.getApprovalType() == null) {
            return null;
        }

        Integer approvalType = task.getApprovalType();
        if (approvalType == 9) {
            return getUpgradeDueTime(task, upgradeDueTimeMap);
        } else if (approvalType == 5 || approvalType == 6 || approvalType == 7 || approvalType == 8) {
            return getNormalDueTime(task, normalDueTimeMap);
        }
        return null;
    }

    /**
     * 处理类型9（产品提资）（圈复杂度=3）
     */
    private String getUpgradeDueTime(ApprovalEntity task,
                                     Map<String, List<DeepenImplementationInnerVo>> upgradeDueTimeMap) {
        if (task.getProjectId() == null || upgradeDueTimeMap == null) {
            return null;
        }
        List<DeepenImplementationInnerVo> vos = upgradeDueTimeMap.get(task.getProjectId());
        return (vos != null && !vos.isEmpty()) ?
                vos.get(0).getRequestInformationCompletionTime() : null;
    }

    /**
     * 处理类型5-8（圈复杂度=6）
     */
    private String getNormalDueTime(ApprovalEntity task,
                                    Map<String, List<LaunchBiddingInnerVo>> normalDueTimeMap) {
        if (task.getProjectId() == null || normalDueTimeMap == null) {
            return null;
        }

        List<LaunchBiddingInnerVo> vos = normalDueTimeMap.get(task.getProjectId());
        if (vos == null || vos.isEmpty()) {
            return null;
        }

        switch (task.getApprovalType()) {
            case 5:
                return vos.get(0).getClarifySubmissionTime();
            case 6:
            case 8:
                return vos.get(0).getConfigureManifestLockTime();
            case 7:
                return vos.get(0).getBiddingDocumentsFinalizationTime();
            default:
                return null;
        }
    }

    //产品提资的规定完成时间(approvalType为9)
    private Map<String, List<DeepenImplementationInnerVo>> allocateUpgradeCompletionTime(List<ApprovalEntity> finalList) {
        //收集approvalType为9的任务项目itemIds集合(当approvalType==9)的时候approval表的projectId字段存储的是项目id
        List<String> itemIds = finalList.stream().filter(entity -> entity.getApprovalType() == 9).map(ApprovalEntity::getProjectId).distinct().collect(Collectors.toList());
        List<DeepenImplementationInnerVo> deepenImplementationList = queryDeepenImplementationList(itemIds);
        return deepenImplementationList.stream().collect(Collectors.groupingBy(DeepenImplementationInnerVo::getId));
    }

    //根据商机id合集RPC获取标书澄清、方案制作、文档编写、招标申请的期望完成时间
    private List<LaunchBiddingInnerVo> queryLaunchBiddingList(List<String> projectIds) {
        //clarifySubmissionTime:标书澄清规定完成时间
        //configureManifestLockTime:方案制作、招标申请规定完成时间
        //biddingDocumentsFinalizationTime:方案制作规定完成时间
        return projectService.queryLaunchBidListByIds(projectIds);
    }

    //根据项目id合集RPC获取产品提资任务的规定完成时间
    private List<DeepenImplementationInnerVo> queryDeepenImplementationList(List<String> itemIds) {
        //requestInformationCompletionTime:规定提资任务完成时间
        return designInnerService.queryInnerDeepenImplementationList(itemIds);
    }

    // 构造去重 key
    private String buildKey(ApprovalEntity entity) {
        return entity.getProjectId() + "_" + entity.getResourceId() + "_" + entity.getApprovalType();
    }

    // 比较两个 createTime 字符串的时间先后（假设格式是标准时间格式，如：yyyy-MM-dd HH:mm:ss）
    private boolean isLater(String time1, String time2) {
        if (time1 == null || time2 == null) {
            return time1 != null;
        }
        return time1.compareTo(time2) > 0;
    }
}
