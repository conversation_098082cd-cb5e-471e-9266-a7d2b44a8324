package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.domain.common.constant.MktTaskConstants;
import com.zte.uedm.dcdigital.domain.repository.ApprovalCommentRepository;
import com.zte.uedm.dcdigital.domain.model.process.ApprovalCommentEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.ApprovalCommentConvert;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ApprovalCommentMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ApprovalCommentPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ApprovalPo;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

@Repository
public class ApprovalCommentRepositoryImpl extends ServiceImpl<ApprovalCommentMapper, ApprovalCommentPo> implements ApprovalCommentRepository {

    @Resource
    private ApprovalCommentMapper approvalCommentMapper;

    @Resource
    private AuthService authService;

    /* Started by AICoder, pid:h67b8ibf26n046414a280b3dc01ffa1185f31c5b */
    /**
     * 添加审批评论信息。
     *
     * @param commentEntity 包含审批评论详细信息的实体对象。
     * @return 如果添加成功，返回 `true`；否则返回 `false`。
     */
    @Override
    public boolean addCommentInfo(ApprovalCommentEntity commentEntity) {
        ApprovalCommentPo approvalCommentPo = ApprovalCommentConvert.INSTANCE.approvalCommentEntityToPo(commentEntity);
        addCommonData(approvalCommentPo);
        int insert = this.baseMapper.insert(approvalCommentPo);
        return insert > 0;
    }

    /**
     * 根据taskId查询审批结果。
     *
     * @param taskIds 包含审批评论详细信息的实体对象。
     * @return 对应审批列表。
     */
    @Override
    public List<ApprovalCommentPo> getCommentInfoByTaskIds(List<String> taskIds) {
        return approvalCommentMapper.getCommentInfoByTaskIds(taskIds);
    }

    /* Started by AICoder, pid:h5de8l09afn44c9146920a65c025a414e728a66e */
    @Override
    public List<ApprovalCommentEntity> queryApprovalCommentEntities(List<String> approvalIds) {
        if (CollectionUtils.isEmpty(approvalIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ApprovalCommentPo> wrapper = Wrappers.<ApprovalCommentPo>lambdaQuery()
                .ne(ApprovalCommentPo::getStaticTaskId, MktTaskConstants.MAIN_TASK_ID)
               .in(ApprovalCommentPo::getApprovalId, approvalIds);
        List<ApprovalCommentPo> poList = this.list(wrapper);
        return ApprovalCommentConvert.INSTANCE.commentsPoToEntities(poList);
    }
    /* Ended by AICoder, pid:h5de8l09afn44c9146920a65c025a414e728a66e */
    /* Ended by AICoder, pid:h67b8ibf26n046414a280b3dc01ffa1185f31c5b */

    @Override
    public List<ApprovalCommentEntity> queryApprovalCommentListByFlowIds(List<String> flowIds) {
        if (CollectionUtils.isEmpty(flowIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ApprovalCommentPo> wrapper = Wrappers.<ApprovalCommentPo>lambdaQuery()
                .in(ApprovalCommentPo::getFlowId, flowIds);
        List<ApprovalCommentPo> poList = this.list(wrapper);
        return ApprovalCommentConvert.INSTANCE.commentsPoToEntities(poList);
    }

    /* Started by AICoder, pid:ba96bd7067gf40e1433e0a917071f91a06b572e8 */
    /**
     * 添加通用数据到审批评论持久化对象。
     *
     * @param approvalCommentPo 需要添加通用数据的审批评论持久化对象。
     */
    private void addCommonData(ApprovalCommentPo approvalCommentPo) {
        String userId = authService.getUserId();
        if (ObjectUtil.isEmpty(approvalCommentPo.getUserId())) {
            approvalCommentPo.setUserId(userId);
        }
        if (ObjectUtil.isEmpty(approvalCommentPo.getCreateTime())) {
            approvalCommentPo.setCreateTime(DateTimeUtils.getCurrentTime());
        }
        approvalCommentPo.setId(UUID.randomUUID().toString());
    }
    /* Ended by AICoder, pid:ba96bd7067gf40e1433e0a917071f91a06b572e8 */

}
