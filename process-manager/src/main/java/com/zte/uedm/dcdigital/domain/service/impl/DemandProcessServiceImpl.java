package com.zte.uedm.dcdigital.domain.service.impl;


/* Started by AICoder, pid:l0d34rdb6a55f63147180acf72e26f8bc8c5e96a */

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.product.DemandManagementLectotypeUpdDto;
import com.zte.uedm.dcdigital.common.bean.product.DemandProcDto;
import com.zte.uedm.dcdigital.common.bean.system.UserVo;
import com.zte.uedm.dcdigital.common.enums.ApprovalTypeEnums;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.common.util.RpcUtil;
import com.zte.uedm.dcdigital.common.web.i18n.I18nUtil;
import com.zte.uedm.dcdigital.domain.common.constant.ProcessConstants;
import com.zte.uedm.dcdigital.domain.common.enums.*;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProcessStatusCode;
import com.zte.uedm.dcdigital.domain.model.approval.ApprovalObj;
import com.zte.uedm.dcdigital.domain.model.process.*;
import com.zte.uedm.dcdigital.domain.repository.ApprovalRepository;
import com.zte.uedm.dcdigital.domain.service.ApprovalCommentService;
import com.zte.uedm.dcdigital.domain.service.DcProcessService;
import com.zte.uedm.dcdigital.domain.service.DcTaskService;
import com.zte.uedm.dcdigital.domain.service.DemandProcessService;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.ApprovalConvert;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ApprovalPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.ApprovalDemandSubmitDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProcessPendingDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.ApprovalDemandVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.ApprovalVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.CommentVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.ManualCommentVo;
import com.zte.uedm.dcdigital.sdk.product.rpc.ProductRpc;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.sdk.system.service.SystemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.constants.BpmnXMLConstants;
import org.flowable.common.engine.impl.identity.Authentication;
import org.flowable.engine.*;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.task.Comment;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.flowable.content.engine.impl.fs.SimpleFileSystemContentStorage.TYPE_TASK;

/**
 * DemandProcessServiceImpl 类实现了 DemandProcessService 接口，提供需求处理的具体实现。
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DemandProcessServiceImpl implements DemandProcessService {

    @Autowired
    private ApprovalRepository approvalRepository; // 注入审批仓库

    @Autowired
    private DcTaskService dcTaskService; // 注入任务服务

    @Autowired
    private DcProcessService dcProcessService; // 注入流程服务

    @Autowired
    private AuthService authService; // 注入认证服务

    @Autowired
    private RuntimeService runtimeService; // 注入运行时服务

    @Autowired
    private ProductRpc productRpc; // 注入产品远程服务

    @Resource
    private SystemService systemService; // 注入系统服务

    @Resource
    private ApprovalCommentService approvalCommentService; // 注入审批评论服务

    @Resource
    private TaskService taskService; // 注入任务服务

    @Resource
    private RepositoryService repositoryService;

    @Resource
    private IdentityService identityService;

    @Resource
    private HistoryService historyService;

    @Autowired
    private ProcessEngine processEngine;

    /**
     * 删除物料处理记录。
     *
     * @param materialId 物料ID
     */
    @Override
    public void delMaterialProcess(String materialId) {
        log.info("delMaterialProcess:{}", materialId);
        ApprovalPo approvalPo = approvalRepository.queryByMaterialId(materialId); // 根据物料ID查询审批记录
        if (null != approvalPo) {
            dcTaskService.stopMProcess(approvalPo.getFlowId()); // 停止审批流程
        }
    }

    @Override
    public void delLectotypeProcess(String lectotypeId) {
        log.info("delLectotypeProcess:{}", lectotypeId);
        ApprovalObj approvalPo = approvalRepository.queryApprovalByLectotypeId(lectotypeId); // 根据物料ID查询审批记录
        if (null != approvalPo&&!approvalPo.getStatus().equals(ApprovalStatusEnum.LTC_FOUR.getCode())) {
            dcTaskService.stopMProcess(approvalPo.getFlowId()); // 停止审批流程
        }
    }

    /**
     * 添加定标处理记录。
     *
     * @param demandProcDto 包含定标处理信息的数据传输对象
     */
    @Override
    public void addLectotypeProcess(DemandProcDto demandProcDto) {
        log.info("demandProcDto:{}", demandProcDto);
        String id = UUID.randomUUID().toString(); // 生成唯一的ID
        ApprovalEntity approvalEntity = new ApprovalEntity(); // 创建审批实体
        approvalEntity.setId(id); // 设置ID
        // 产品小类id
        approvalEntity.setResourceId(demandProcDto.getProductCategoryId()); // 设置资源ID
        approvalEntity.setApprovalType(Integer.valueOf(ApprovalTypeEnums.LECTOTYPE_APPROVAL.getId())); // 设置审批类型
        approvalEntity.setSubmitUser(authService.getUserId()); // 设置提交用户
        approvalEntity.setSubmitTime(DateTimeUtils.getCurrentTime()); // 设置提交时间
        approvalEntity.setCreateTime(DateTimeUtils.getCurrentTime()); // 设置创建时间
        approvalEntity.setUpdateTime(DateTimeUtils.getCurrentTime()); // 设置更新时间
        approvalEntity.setCreateBy(authService.getUserId()); // 设置创建者
        approvalEntity.setUpdateBy(authService.getUserId()); // 设置更新者
        approvalEntity.setBussesDataJson(demandProcDto.getLectotypeId());
        approvalEntity.setBillQuantityPerson(demandProcDto.getLectotypeName());
        // 设置流程任务标题
        approvalEntity.setTitle(ProcessEnum.LECTOTYPE.getProcessName() + ":"+demandProcDto.getLectotypeName()+",资料准备中"); // 设置标题
        approvalEntity.setStatus(ApprovalStatusEnum.LTC_ONE.getCode());
        HashMap<String, Object> map = new HashMap<>(); // 创建参数映射
        map.put(ProcessConstants.COST_DIRECTOR, demandProcDto.getCostDirector()); // 设置成本主管
        map.put(ProcessConstants.MATERIAL_ASSISTANTS, demandProcDto.getMaterialAssistants()); // 设置物料助手
        map.put(ProcessConstants.LETOTYPE_ID, demandProcDto.getLectotypeId()); // 设置定标ID

        approvalRepository.createApproval(approvalEntity); // 创建审批记录
        // 标书分解、物料选型、文档编写共用的同一个流程
        String processKey = ProcessEnum.LECTOTYPE.getProcessKey(); // 获取流程键
        String flowId = startProcessByKey(processKey, id, map); // 启动流程
        approvalRepository.updateFlowIdById(id, flowId); // 更新审批记录的流程ID

        // 若第一个用户任务为发起人，则自动完成任务，如不是则继续
//        List<Task> tasks = taskService.createTaskQuery().processInstanceId(flowId).list();
//        for (Task t : tasks) {
//            log.info("startFistTask, processInstanceId: {},{}", flowId, t);
//            taskService.addComment(t.getId(), flowId, null, ProcessEnum.LECTOTYPE.getProcessName() + ":创建流程");
//        }


    }

    public String startProcessByKey(String processKey, String processBusinessKey, Map<String, Object> variables) {
        try {
            // 查询最新的流程定义
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionKey(processKey)
                    .latestVersion()
                    .singleResult();

            // 调用私有方法启动流程实例
            return startProcess(processDefinition, processBusinessKey, variables);
        } catch (Exception e) {
            // 记录错误日志
            log.error("启动流程失败, 错误: {}", e);
            // 抛出自定义业务异常，表示流程启动失败
            throw new BusinessException(ProcessStatusCode.PROCESS_START_FAILED);
        }
    }

    private String startProcess(ProcessDefinition processDefinition, String processBusinessKey, Map<String, Object> variables) {
        if(ObjectUtil.isNull(processDefinition)){
            log.error("process definition not found,processKey={}", processDefinition == null ? "null" : processDefinition.getKey());
            throw new BusinessException(ProcessStatusCode.PROCESS_DEFINITION_NOT_FOUND);
        }

        if(ObjectUtil.isNotNull(processDefinition) && processDefinition.isSuspended()){
            log.error("process definition is suspended,processKey={}", processDefinition.getKey());
            throw new BusinessException(ProcessStatusCode.PROCESS_SUSPENDED);
        }

        ProcessInstance existingInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processBusinessKey).singleResult();
        if(ObjectUtil.isNotNull(existingInstance)){
            log.error("process instance already exist,processBusinessKey={}", processBusinessKey);
            throw new BusinessException(ProcessStatusCode.PROCESS_INSTANCE_EXIST);
        }
        // 设置流程发起人id到流程中
        String userId = authService.getUserId();
        identityService.setAuthenticatedUserId(userId);
        variables.put(BpmnXMLConstants.ATTRIBUTE_EVENT_START_INITIATOR, userId);
        // 设置流程状态为进行中
        variables.put(ProcessConstants.PROCESS_STATUS_KEY, ProcessStatusEnum.RUNNING.getStatus());
        // 发起流程实例
        ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinition.getId(), processBusinessKey, variables);
        // 第一个用户任务为发起人，则自动完成任务
        //dcTaskService.startFistTask(processInstance, variables);
        return processInstance.getId();
    }

    /**
     * 根据审批ID获取审批详情。
     *
     * @param approvalId 审批ID
     * @return 审批详情
     */
    @Override
    public ApprovalDemandVo getApprovalById(String approvalId) {
        ApprovalObj approvalObj = approvalRepository.getById(approvalId);
        log.info("approvalObj: {}", approvalObj);
        ApprovalDemandVo approvalVo = new ApprovalDemandVo();

        approvalVo.setId( approvalObj.getId() );
        approvalVo.setTitle( approvalObj.getTitle() );
        approvalVo.setApprovalType( approvalObj.getApprovalType() );
        approvalVo.setResourceId( approvalObj.getResourceId() );
        approvalVo.setSubmitUser( approvalObj.getSubmitUser() );
        approvalVo.setSubmitTime( approvalObj.getSubmitTime() );
        approvalVo.setFlowId( approvalObj.getFlowId() );
        approvalVo.setStatus( approvalObj.getStatus() );
        approvalVo.setCreateTime( approvalObj.getCreateTime() );
        approvalVo.setUpdateTime( approvalObj.getUpdateTime() );
        approvalVo.setCreateBy( approvalObj.getCreateBy() );
        approvalVo.setUpdateBy( approvalObj.getUpdateBy() );
        approvalVo.setLectotypeId(approvalObj.getBussesDataJson());
        approvalVo.setLectotypeName(approvalObj.getBillQuantityPerson());
        log.info("approvalVo: {}", approvalVo);
        List<Comment> commentList = new ArrayList<>();
        UserVo userInfo = systemService.getUserinfoById(approvalVo.getSubmitUser());
        if (userInfo != null) {
            approvalVo.setSubmitUser(userInfo.getDisplayText());
        }
        List<ProcessNodeDetailEntity> processNodeDetailEntities = dcProcessService.processNodeDetail(approvalObj.getFlowId());
        log.info("processNodeDetailEntities={}", processNodeDetailEntities);
        List<String> roleList = new ArrayList<>();
        processNodeDetailEntities.forEach(processNodeDetail -> {
            if (ObjectUtil.isNotNull(processNodeDetail.getCommentList()) && ObjectUtil.isNotEmpty(processNodeDetail.getCommentList())) {
                commentList.addAll(processNodeDetail.getCommentList());
                //收集角色名称
                roleList.add(processNodeDetail.getActivityName());
            }
        });
        log.info("commentList={}", JSON.toJSONString(commentList));
        if (commentList.size() > 0) {
            List<String> userIds = commentList.stream().map(Comment::getUserId).collect(Collectors.toList());
            log.info("userIds={}", JSON.toJSONString(userIds));
            List<UserVo> userVos = systemService.getUserinfoByIds(userIds);
            Map<String, String> map = userVos.stream().collect(Collectors.toMap(UserVo::getId, UserVo::getDisplayText));
            List<ManualCommentVo> comments = new ArrayList<>();
            commentList.forEach(comment -> {
                String id = comment.getId();
                String userId = comment.getUserId();
                String time = DateTimeUtils.getStringTime(comment.getTime());
                String type = comment.getType();
                Integer result = Integer.valueOf(type);
                String fullMessage = comment.getFullMessage();
                String role = roleList.get(commentList.indexOf(comment)) + " " + map.get(userId);
                //comments.add(new CommentVo(id, role, time, result, fullMessage));
                comments.add(new ManualCommentVo(id, role, time, result, fullMessage,I18nUtil.getI18nFromString(FlowCommentEnum.getRemarkByType(type), I18nUtil.getLanguage())));
            });
            approvalVo.setComments(comments);
        }

        return approvalVo;
    }
    /**
     * 提交审批单。
     *
     * @param submitDto 包含提交信息的数据传输对象
     */
    @Override
    public void submitApproval(ApprovalDemandSubmitDto submitDto) {
        log.info("submitDto: {}", submitDto);
        TaskBo taskBo = new TaskBo();
        taskBo.setTaskId(submitDto.getTaskId());
        taskBo.setProcInsId(submitDto.getFlowId());
        taskBo.setComment(submitDto.getComment());
        //查询当前审批单
        ApprovalEntity approvalEntity = approvalRepository.queryByFlowId(submitDto.getFlowId());

        taskBo.setProcessBusinessKey(approvalEntity.getId());


        //改任务状态
        ApprovalEntity approvalEntity1 = new ApprovalEntity();
        approvalEntity1.setId(approvalEntity.getId());
        Integer lectotypeStatus = 1;
        //接收即更改为"处理中"
        if (ApprovalStatusEnum.LTC_ONE.getCode() == approvalEntity.getStatus()) {
            approvalEntity1.setStatus(ApprovalStatusEnum.LTC_TWO.getCode());
            approvalEntity1.setTitle(ProcessEnum.LECTOTYPE.getProcessName()+":"+approvalEntity.getBillQuantityPerson() + ",已完成准备,待提交申请");
            lectotypeStatus=9;
            taskBo.setComment("已完成准备 "+taskBo.getComment());
            taskBo.setResult(21);
        } else if (ApprovalStatusEnum.LTC_TWO.getCode() == approvalEntity.getStatus() && submitDto.getApprovalResult() == 0) {
            approvalEntity1.setStatus(ApprovalStatusEnum.LTC_ONE.getCode());
            approvalEntity1.setTitle(ProcessEnum.LECTOTYPE.getProcessName()+":"+approvalEntity.getBillQuantityPerson() + ",退回，重新准备资料");
             lectotypeStatus=9;
            taskBo.setComment("退回 "+taskBo.getComment());
            taskBo.setResult(2);
        } else if (ApprovalStatusEnum.LTC_TWO.getCode() == approvalEntity.getStatus() && submitDto.getApprovalResult() == 1) {
            approvalEntity1.setStatus(ApprovalStatusEnum.LTC_THREE.getCode());
            approvalEntity1.setTitle(ProcessEnum.LECTOTYPE.getProcessName()+":"+approvalEntity.getBillQuantityPerson() + ",申请已提交，待处理");
             lectotypeStatus=2;
            taskBo.setComment("申请提交 "+taskBo.getComment()+"【URL:"+submitDto.getTsUrl()+"】");
            taskBo.setResult(22);
        } else if (ApprovalStatusEnum.LTC_THREE.getCode() == approvalEntity.getStatus()) {
            approvalEntity1.setStatus(ApprovalStatusEnum.LTC_FOUR.getCode());
            approvalEntity1.setTitle(ProcessEnum.LECTOTYPE.getProcessName()+":"+approvalEntity.getBillQuantityPerson() + ",申请已通过");
             lectotypeStatus=3;
            taskBo.setComment("申请通过 "+taskBo.getComment());
            taskBo.setResult(20);
        }
        //执行任务
        handTask(taskBo, approvalEntity.getStatus(),submitDto.getApprovalResult());

        approvalEntity1.setUpdateBy(authService.getUserId());
        approvalEntity1.setUpdateTime(DateTimeUtils.getCurrentTime());
        //更新 approval表单
        approvalRepository.updateApprovalStatus(approvalEntity1);


        //回填tsUrl
        if (submitDto.getApprovalResult() == 1 && ApprovalStatusEnum.LTC_TWO.getCode() == approvalEntity.getStatus()) {

            DemandManagementLectotypeUpdDto dto = new DemandManagementLectotypeUpdDto();
            dto.setLectotypeId(approvalEntity.getBussesDataJson()); // 设置定标ID
            dto.setBidUrl(submitDto.getTsUrl()); // 设置定标状态
            dto.setLectotypeStatus(lectotypeStatus);
            dto.setUpdateBy(authService.getUserId());
            dto.setUpdateTime(DateTimeUtils.getCurrentTime());
            log.info("dto:{}", dto);
            RpcUtil.call(productRpc.updLectotype(dto)); // 调用远程
        } else {

            DemandManagementLectotypeUpdDto dto = new DemandManagementLectotypeUpdDto();
            dto.setLectotypeId(approvalEntity.getBussesDataJson()); // 设置定标ID
            dto.setLectotypeStatus(lectotypeStatus);
            dto.setUpdateBy(authService.getUserId());
            dto.setUpdateTime(DateTimeUtils.getCurrentTime());
            log.info("dto:{}", dto);
            RpcUtil.call(productRpc.updLectotype(dto)); // 调用远程

        }
    }

    /**
     * 处理任务。
     *
     * @param taskBo         任务对象
     */
    private void handTask(TaskBo taskBo,Integer type,Integer approvalResult) {
        Task task = taskService.createTaskQuery().taskId(taskBo.getTaskId()).singleResult();
        if (ObjectUtil.isNull(task)) {
            log.error("task not found, taskId:{}", taskBo.getTaskId());
            throw new BusinessException(ProcessStatusCode.TASK_NOT_FOUND);
        }
        if (task.isSuspended()) {
            throw new BusinessException(ProcessStatusCode.TASK_SUSPENDED);
        }
        // 获取当前处理人
        String userId = authService.getUserId();
        //用户拾取任务
        taskService.setAssignee(task.getId(), userId);
        //需要添加这段代码，否则审批意见表ACT_HI_COMMENT审批的userid是空的
        Authentication.setAuthenticatedUserId(userId);
        if (ApprovalStatusEnum.LTC_TWO.getCode() == type){
            Map<String, Object> variables = new HashMap<>();
            variables.put("approvalResult", approvalResult);
            taskService.addComment(taskBo.getTaskId(), taskBo.getProcInsId(), String.valueOf(taskBo.getResult()), taskBo.getComment());
            // 执行任务
            taskService.complete(taskBo.getTaskId(), variables);
        }else {
            taskService.addComment(taskBo.getTaskId(), taskBo.getProcInsId(), String.valueOf(taskBo.getResult()), taskBo.getComment());
            // 执行任务
            taskService.complete(taskBo.getTaskId());
        }



        //添加comment
        ApprovalCommentEntity commentEntity = new ApprovalCommentEntity();
        //审批单id
        commentEntity.setApprovalId(taskBo.getProcessBusinessKey());
        commentEntity.setComment(taskBo.getComment());
        commentEntity.setResult(taskBo.getResult());
        commentEntity.setNodeId(task.getId());
        commentEntity.setNodeName(task.getName());
        commentEntity.setFlowId(task.getProcessInstanceId());
        commentEntity.setStaticTaskId(task.getTaskDefinitionKey());
        commentEntity.setUserId(userId);
        approvalCommentService.addComment(commentEntity);
    }

    /* Started by AICoder, pid:5f9dew2faal26d3144e20b6931a640825a140a0f */

    /**
     * 查询待处理审批列表。
     *
     * @param queryDto 查询条件数据传输对象
     * @return 分页的审批对象列表
     */
    @Override
    public PageVO<ApprovalObj> queryPendingList(ProcessPendingDto queryDto) {
        String userId = authService.getUserId();
        log.info("userId: {}", userId);

        List<ApprovalObj> approvalObjs = new ArrayList<>();

        // 查询待办任务列表
        List<TaskEntity> taskEntityList = todoList(userId);
        if (taskEntityList.size() == 0) {
            return new PageVO<>(0, new ArrayList<>());
        }

//        Map<String, String> insIdTaskIdMap = taskEntityList.stream().collect(Collectors.toMap(TaskEntity::getProcInsId, TaskEntity::getTaskId));
//        List<String> insIds = taskEntityList.stream().map(TaskEntity::getProcInsId).collect(Collectors.toList());
        //历史遗留问题，之前存在多个任务共用一个流程实例id,导致组装map报错，但为了兼容老数据，暂时保留
        // 第一步：计算每个 procInsId 的出现次数
        Map<String, Long> procInsIdCounts = taskEntityList.stream()
                .map(TaskEntity::getProcInsId)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        // 第二步：过滤出仅出现一次的 procInsId
        Set<String> uniqueProcInsIds = procInsIdCounts.entrySet().stream()
                .filter(entry -> entry.getValue() == 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
        // 第三步：基于唯一 procInsId 构建 insIdTaskIdMap 和 insIds 列表
        Map<String, String> insIdTaskIdMap = taskEntityList.stream()
                .filter(task -> uniqueProcInsIds.contains(task.getProcInsId()))
                .collect(Collectors.toMap(TaskEntity::getProcInsId, TaskEntity::getTaskId));

        List<String> insIds = new ArrayList<>(uniqueProcInsIds);
        queryDto.setProcInsIds(insIds);

        Page<ApprovalObj> voPage = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());
        approvalObjs = approvalRepository.queryPendingListNew(queryDto);
        log.info("approvalObjs: {}", approvalObjs);
        if (null == approvalObjs || approvalObjs.size() == 0) {
            return new PageVO<>(0, new ArrayList<>());
        }
        List<String> submitUserIds = approvalObjs.stream().map(ApprovalObj::getSubmitUser).collect(Collectors.toList());
        log.info("submitUserIds: {}", submitUserIds);
        List<UserVo> userinfoByIds = systemService.getUserinfoByIds(submitUserIds);
        Map<String, String> map = userinfoByIds.stream().collect(Collectors.toMap(UserVo::getId, UserVo::getDisplayText));
        approvalObjs.forEach(approvalObj -> {
            approvalObj.setTaskId(insIdTaskIdMap.get(approvalObj.getFlowId()));
            approvalObj.setSubmitUser(map.get(approvalObj.getSubmitUser()));
        });
        // 根据taskId获取对应流程节点key，并将节点key一并返回
        enrichWithTaskKeys(approvalObjs); // 为列表补充节点Key
        //根据流程节点key设置"产品提资任务"不同阶段的处理类型
        enrichProductSubmission(approvalObjs);
        return new PageVO<>(voPage.getTotal(), approvalObjs);
    }

    /**
     * 获取用户的待办任务列表。
     *
     * @param userId 用户ID
     * @return 待办任务列表
     */
    public List<TaskEntity> todoList(String userId) {
        TaskQuery taskQuery = taskService.createTaskQuery()
                .active()
                .includeProcessVariables()
                .taskCandidateOrAssigned(userId);

        // 构建搜索条件
        List<Task> tasks = taskQuery.list();
        log.info("tasks: {}", tasks);
        List<TaskEntity> flowList = new ArrayList<>();
        if (null == tasks || tasks.size() == 0) {
            return flowList;
        }
        for (Task task : tasks) {
            TaskEntity flowTask = new TaskEntity();
            // 设置当前流程信息
            flowTask.setTaskId(task.getId());
            flowTask.setTaskDefKey(task.getTaskDefinitionKey());
            flowTask.setCreateTime(task.getCreateTime());
            flowTask.setProcDefId(task.getProcessDefinitionId());
            flowTask.setTaskName(task.getName());
            // 查询并设置流程定义信息
            ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(task.getProcessDefinitionId())
                    .singleResult();
            flowTask.setDeployId(pd.getDeploymentId());
            flowTask.setProcDefName(pd.getName());
            flowTask.setProcDefVersion(pd.getVersion());
            flowTask.setProcInsId(task.getProcessInstanceId());
            // 在approve表根据流程实例id查询流程状态
            log.info("current flowId: {}", task.getProcessInstanceId());
            ApprovalObj approvalObj = approvalRepository.getByFlowId(task.getProcessInstanceId());
            flowTask.setCategory(String.valueOf(approvalObj.getApprovalType()));
            flowTask.setProcessStatus(String.valueOf(approvalObj.getStatus()));
            // 查询并设置流程发起人信息
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(task.getProcessInstanceId())
                    .singleResult();
            flowTask.setStartUserId(historicProcessInstance.getStartUserId());
            // 添加到待办任务列表
            flowList.add(flowTask);
        }
        return flowList;
    }

    /**
     * 为审批对象列表补充任务节点Key。
     *
     * @param approvalObjs 审批对象列表
     * @return 补充后的审批对象列表
     */
    public List<ApprovalObj> enrichWithTaskKeys(List<ApprovalObj> approvalObjs) {
        // 1. 提取所有需要查询的TaskID
        Set<String> taskIds = approvalObjs.stream()
                .map(ApprovalObj::getTaskId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (taskIds.isEmpty()) {
            return approvalObjs;
        }

        // 2. 批量查询任务信息（减少数据库交互次数）
        Map<String, String> taskKeyMap = processEngine.getTaskService().createTaskQuery()
                .taskIds(taskIds)
                .list()
                .stream()
                .collect(Collectors.toMap(
                        Task::getId,
                        Task::getTaskDefinitionKey
                ));

        // 3. 为每个对象设置节点Key
        approvalObjs.forEach(obj -> {
            String taskKey = taskKeyMap.get(obj.getTaskId());
            obj.setProcDefKey(taskKey);
        });

        return approvalObjs;
    }
    /* Ended by AICoder, pid:5f9dew2faal26d3144e20b6931a640825a140a0f */

    /**
     *  根据流程节点id设置"产品提资任务"不同的处理类型
     * */
    private void enrichProductSubmission(List<ApprovalObj> approvalObjs) {
        approvalObjs.forEach(approvalObj -> {
            if (ProcessEnum.PRODUCT_SUBMISSION_TASK.getType().equals(approvalObj.getApprovalType().toString())) {
                approvalObj.setFlag(ProductUpgradeProcessNodeEnum.getNumberById(approvalObj.getProcDefKey()));
            }
        });

    }
}
/* Ended by AICoder, pid:l0d34rdb6a55f63147180acf72e26f8bc8c5e96a */