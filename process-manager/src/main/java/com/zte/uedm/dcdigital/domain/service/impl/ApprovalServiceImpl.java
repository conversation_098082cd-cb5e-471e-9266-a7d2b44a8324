package com.zte.uedm.dcdigital.domain.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.dto.ApprovalInformationDto;
import com.zte.uedm.dcdigital.common.bean.dto.MaterialDto;
import com.zte.uedm.dcdigital.common.bean.enums.ApprovalResult;
import com.zte.uedm.dcdigital.common.bean.enums.system.RoleCodeEnum;
import com.zte.uedm.dcdigital.common.bean.process.ApprovalCommonVo;
import com.zte.uedm.dcdigital.common.bean.process.MaterialToApprovalDto;
import com.zte.uedm.dcdigital.common.bean.process.NewApprovalDto;
import com.zte.uedm.dcdigital.common.bean.project.ProjectDetailInfoVo;
import com.zte.uedm.dcdigital.common.bean.system.RoleVo;
import com.zte.uedm.dcdigital.common.bean.system.UserVo;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.common.web.i18n.I18nUtil;
import com.zte.uedm.dcdigital.domain.common.constant.MktTaskConstants;
import com.zte.uedm.dcdigital.domain.common.constant.ProcessConstants;
import com.zte.uedm.dcdigital.domain.common.constant.TaskConstants;
import com.zte.uedm.dcdigital.domain.common.enums.*;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProcessRoleStatusCode;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProcessStatusCode;
import com.zte.uedm.dcdigital.domain.model.approval.ApprovalObj;
import com.zte.uedm.dcdigital.domain.model.approval.ApprovalWithResultObj;
import com.zte.uedm.dcdigital.domain.model.approval.MktApprovalAssocInfoPo;
import com.zte.uedm.dcdigital.domain.model.process.*;
import com.zte.uedm.dcdigital.domain.repository.ApprovalRepository;
import com.zte.uedm.dcdigital.domain.repository.MktApprovalAssocInfoRepository;
import com.zte.uedm.dcdigital.domain.service.ApprovalService;
import com.zte.uedm.dcdigital.domain.service.DcProcessService;
import com.zte.uedm.dcdigital.domain.service.DcTaskService;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.ApprovalConvert;
import com.zte.uedm.dcdigital.interfaces.web.dto.*;
import com.zte.uedm.dcdigital.interfaces.web.vo.*;
import com.zte.uedm.dcdigital.sdk.product.service.ProductService;
import com.zte.uedm.dcdigital.sdk.project.service.ProjectService;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.sdk.system.service.SystemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.ProcessEngine;
import org.flowable.engine.task.Comment;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ApprovalServiceImpl implements ApprovalService {
    ObjectMapper mapper = new ObjectMapper();
    @Resource
    private ApprovalRepository approvalRepository;
    @Resource
    private MktApprovalAssocInfoRepository mktApprovalAssocInfoRepository;
    @Resource
    private AuthService authService;
    @Resource
    private SystemService systemService;
    @Resource
    private ProductService productService;
    @Autowired
    private DcProcessService dcProcessService;
    @Autowired
    private DcTaskService dcTaskService;

    @Autowired
    ProcessEngine processEngine;

    @Autowired
    @Qualifier("sdkProjectServiceImpl")
    private ProjectService projectService;

    private String POSITIVE_SORT = "asc";
    private String REVERSE_SORT = "desc";
    /* Started by AICoder, pid:66ec1x06a3m60f114c5808c160d9f52ff09217a5 */

    /**
     * 更新审批流程的状态。
     *
     * @param processId 流程实例ID，用于标识特定的审批流程实例。
     * @param status    新的审批状态。
     * @return 如果更新成功，返回 `true`；否则返回 `false`。
     */
    @Override
    @Transactional
    public boolean updateApprovalStatus(String processId, Integer status) {
        ApprovalEntity approvalEntity = approvalRepository.queryByFlowId(processId);
        if (approvalEntity == null) {
            log.error("data not found, processId: {}", processId);
            return true;
        }
        approvalEntity.setStatus(status);
        // 获取更新人
        String userId = authService.getUserId();
        approvalEntity.setUpdateBy(userId);
        String currentTime = DateTimeUtils.getCurrentTime();
        approvalEntity.setUpdateTime(currentTime);
        approvalEntity.setHandlerTime(currentTime);
        return approvalRepository.updateApprovalStatus(approvalEntity);
    }
    /* Ended by AICoder, pid:66ec1x06a3m60f114c5808c160d9f52ff09217a5 */

    @Override
    public PageVO<ApprovalObj> queryPendingList(PendingApprovalQueryDto queryDto) {
        String userId = authService.getUserId();
        log.info("userId: {}", userId);
        ProcessBo processBo = new ProcessBo();
        processBo.setUserId(userId);
        processBo.setPageNum(queryDto.getPageNum());
        processBo.setPageSize(queryDto.getPageSize());
        List<ApprovalObj> approvalObjs = new ArrayList<>();
        //String typeStr = String.valueOf(queryDto.getType());
        //boolean condition1 = MktBidTaskClassEnum.MARKET_SUPPORT_BIDDING.getCode().equals(typeStr);
        //boolean condition2 = queryDto.isQueryConditionEmpty();
//        if (condition1 || condition2) {
//            //如果是查询包含"市场投标支持"类型的审批需要特殊处理
//            PageVO<ApprovalObj> pageVO=specialQueryPendingList(queryDto);
//            return pageVO;
//        }
        if (!queryDto.isQueryConditionEmpty()) {
            List<String> userIds = new ArrayList<>();
            if (!StringUtils.isEmpty(queryDto.getSubmitUser())) {
                List<UserVo> userVos = systemService.getUserinfoBySearchText(queryDto.getSubmitUser());
                userIds = userVos.stream().map(UserVo::getId).collect(Collectors.toList());
                log.info("user ids: {}", userIds);
                if (userIds.isEmpty()) {
                    return new PageVO<>(0, new ArrayList<>());
                }
            }
            PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());
            log.info("queryPendingList:{}", queryDto);
            approvalObjs = approvalRepository.queryPendingList(queryDto, userIds);
            List<String> approvalIds = approvalObjs.stream().map(ApprovalObj::getFlowId).collect(Collectors.toList());
            log.info("approvalIds: {}", approvalIds);
//            String materialTypeStr = String.valueOf(queryDto.getType());
//            processBo.setProcessKey(ProcessEnum.fromType(materialTypeStr).getProcessKey());
            processBo.setProcInsIds(approvalIds);
        }
        // 查询待办任务列表
        PageVO<TaskEntity> todoList = dcProcessService.todoList(processBo);
        List<TaskEntity> taskEntityList = todoList.getList();
        if (isEmptyOrReturnEmptyPage(taskEntityList)) {
            return new PageVO<>(0, new ArrayList<>());
        }
        log.info("taskEntityList: {}", taskEntityList);
        Map<String, String> insIdTaskIdMap = taskEntityList.stream().collect(Collectors.toMap(TaskEntity::getProcInsId, TaskEntity::getTaskId));
        List<String> insIds = taskEntityList.stream().map(TaskEntity::getProcInsId).collect(Collectors.toList());
        approvalObjs = approvalObjs.stream().filter(approvalObj -> insIds.contains(approvalObj.getFlowId())).collect(Collectors.toList());
        if (queryDto.isQueryConditionEmpty()) {
            approvalObjs = approvalRepository.getByFlowIds(insIds);
        }
        log.info("approvalObjs: {}", approvalObjs);
        if (isEmptyOrReturnEmptyPage(approvalObjs)) {
            return new PageVO<>(0, new ArrayList<>());
        }
        List<String> submitUserIds = approvalObjs.stream().map(ApprovalObj::getSubmitUser).collect(Collectors.toList());
        log.info("submitUserIds: {}", submitUserIds);
        List<UserVo> userinfoByIds = systemService.getUserinfoByIds(submitUserIds);
        Map<String, String> map = userinfoByIds.stream().collect(Collectors.toMap(UserVo::getId, UserVo::getDisplayText));
        approvalObjs.forEach(approvalObj -> {
            approvalObj.setTaskId(insIdTaskIdMap.get(approvalObj.getFlowId()));
            approvalObj.setSubmitUser(map.get(approvalObj.getSubmitUser()));
        });
        approvalObjs=determineSortOrder(approvalObjs,queryDto);
        int total = (int)todoList.getTotal();
        int totalPages = (int) Math.ceil((double) total / queryDto.getPageSize());
        int fromIndex = (queryDto.getPageNum() - 1) * queryDto.getPageSize();
        int toIndex = Math.min(fromIndex + queryDto.getPageSize(), total);
        List<ApprovalObj> subList = approvalObjs.subList(fromIndex, toIndex);
        //根据taskId获取对应流程节点key,并将节点key一并返回
        enrichWithTaskKeys(subList); // 为列表补充节点Key
        PageVO<ApprovalObj> pageVO = new PageVO<>(total, subList);
        pageVO.setPages(totalPages);
        return pageVO;
    }
    //查询"市场投标支持"类型任务以及所有类型
    public PageVO<ApprovalObj> specialQueryPendingList(PendingApprovalQueryDto queryDto) {
        String userId = authService.getUserId();
        log.info("userId: {}", userId);
        ProcessBo processBo = new ProcessBo();
        processBo.setUserId(userId);
        processBo.setPageNum(queryDto.getPageNum());
        processBo.setPageSize(queryDto.getPageSize());
        String typeStr = String.valueOf(queryDto.getType());
        boolean condition = MktBidTaskClassEnum.MARKET_SUPPORT_BIDDING.getCode().equals(typeStr);
        if (condition){
            //只查询市场投标支持类型
            processBo.setProcessKey(ProcessEnum.MARKET_BID_SUPPORT.getProcessKey());
        }
        List<ApprovalObj> approvalObjs = new ArrayList<>();
        // 查询待办任务列表
        PageVO<TaskEntity> todoList = dcProcessService.todoList(processBo);
        List<TaskEntity> taskEntityList = todoList.getList();
        if (isEmptyOrReturnEmptyPage(taskEntityList)) {
            return new PageVO<>(0, new ArrayList<>());
        }
        //这里需要特殊处理一下物料任务的名称、物料的流程id(物料相关任务的名称存储在approval表中)
        Map<String, ApprovalEntity> flowIdApprovalIdMap = getFlowIdApprovalIdMaps(taskEntityList);
        log.info("flowIdApprovalIdMap: {}",flowIdApprovalIdMap);
        Map<String, String> approvalUserinfoMap =getApprovalUserinfoMap(taskEntityList);
        for (TaskEntity taskEntity : taskEntityList) {
            ApprovalObj approvalObj = new ApprovalObj();
            //物料任务需要设置流程id，"市场投标支持"任务不能设置流程id，因为"市场投标支持"任务是一对多，主、子任务共用流程id,这里处理一下
            approvalObj.setId(taskEntity.getProcInsId()==null?"":flowIdApprovalIdMap.get(taskEntity.getProcInsId()).getId());
            approvalObj.setTaskId(taskEntity.getTaskId());
            approvalObj.setFlowId(taskEntity.getProcInsId());
            approvalObj.setBillQuantityPerson(flowIdApprovalIdMap.get(taskEntity.getProcInsId()).getBillQuantityPerson());
            approvalObj.setBillQuantityId(flowIdApprovalIdMap.get(taskEntity.getProcInsId()).getBillQuantityId());
            approvalObj.setProjectId(flowIdApprovalIdMap.get(taskEntity.getProcInsId()).getProjectId());
            //物料任务的名称需要去approval表中取值，这里处理一下
            approvalObj.setTitle(taskEntity.getCategory().equals(ProcessEnum.MARKET_BID_SUPPORT.getType())?taskEntity.getTaskName():flowIdApprovalIdMap.get(taskEntity.getProcInsId()).getTitle());
            approvalObj.setApprovalType(Integer.valueOf(taskEntity.getCategory()));
            String userName= approvalUserinfoMap.get(taskEntity.getStartUserId());
            if (userName==null||"".equals(userName)){
                approvalObj.setSubmitUser(taskEntity.getStartUserId());
            }else {
                approvalObj.setSubmitUser(userName);
            }
            approvalObj.setSubmitTime(DateTimeUtils.getStringTime(taskEntity.getCreateTime()));
            approvalObj.setStatus(Integer.valueOf(taskEntity.getProcessStatus()));
            approvalObj.setProcDefKey(taskEntity.getTaskDefKey());
            //当前流程key如果满足以下要求就映射"市场投标支持"任务标识符
            if (MktTaskConstants.MAIN_TASK_ID.equals(taskEntity.getTaskDefKey())) {
                approvalObj.setProcDefKey(MktTaskConstants.ONE);
            }else if (MktTaskConstants.REP_MAIN_TASK_ID.equals(taskEntity.getTaskDefKey())) {
                approvalObj.setProcDefKey(MktTaskConstants.TWO);
            } else if (MktTaskConstants.SUB_TASK_IDS.contains(taskEntity.getTaskDefKey())) {
                approvalObj.setProcDefKey(MktTaskConstants.THREE);
            }else if (MktTaskConstants.VERIFY_TASK_IDS.contains(taskEntity.getTaskDefKey())) {
                approvalObj.setProcDefKey(MktTaskConstants.FOUR);
            }
            approvalObjs.add(approvalObj);
        }
        approvalObjs=determineSortOrder(approvalObjs,queryDto);
        int total = (int)todoList.getTotal();
        PageVO<ApprovalObj> pageVO = new PageVO<>(total,approvalObjs);
        pageVO.setPages(todoList.getPages());
        return pageVO;
    }
    //抽离出来的获取当前任务用户id集合的方法(一次性查询符合要求的用户信息避免RPC循环调用)
    private Map<String, String> getApprovalUserinfoMap(List<TaskEntity> taskEntityList) {
        //收集用户id然后获取姓名+工号
        List<String> userIds= taskEntityList.stream().map(TaskEntity::getStartUserId).collect(Collectors.toList());
        List<UserVo> userVos=systemService.getUserinfoByIds(userIds);
        Map<String, String> approvalUserinfoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(userVos)) {
            // 遍历approvalEntities列表，构造flowId到ApprovalEntity的id的映射
            for (UserVo userVo : userVos) {
                String userId = userVo.getId();
                String userName = userVo.getDisplayText();
                approvalUserinfoMap.put(userId, userName);
            }
        }
        return approvalUserinfoMap;
    }
    //抽离出来的获取当前任务流程对象的方法
    private Map<String, ApprovalEntity> getFlowIdApprovalIdMaps(List<TaskEntity> taskEntityList) {
        List<String> flowIds = taskEntityList.stream().map(TaskEntity::getProcInsId).collect(Collectors.toList());
        //根据flowIds查询approval表收集approvalId用于物料上架、下架、变更使用
        List<ApprovalEntity> approvalEntities=approvalRepository.queryApprovalListByFlowIds(flowIds);
        Map<String, ApprovalEntity> flowIdApprovalIdMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(approvalEntities)) {
            // 遍历approvalEntities列表，构造flowId到ApprovalEntity的id的映射
            for (ApprovalEntity approvalEntity : approvalEntities) {
                String flowId = approvalEntity.getFlowId();
                String approvalId = approvalEntity.getId();
                flowIdApprovalIdMap.put(flowId, approvalEntity);
            }
        }
        return flowIdApprovalIdMap;
    }
    // 抽离出来的检查方法
    private <T> boolean isEmptyOrReturnEmptyPage(List<T> list) {
        if (list == null || list.isEmpty()) {
            return true;
        }
        return false;
    }

    //根据taskId获取对应流程节点key,并将节点key一并返回
    // 新增：获取流程节点Key的逻辑
    public List<ApprovalObj> enrichWithTaskKeys(List<ApprovalObj> approvalObjs) {
        // 1. 提取所有需要查询的TaskID
        Set<String> taskIds = approvalObjs.stream()
                .map(ApprovalObj::getTaskId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (taskIds.isEmpty()) {
            return approvalObjs;
        }

        // 2. 批量查询任务信息（减少数据库交互次数）
        Map<String, String> taskKeyMap = processEngine.getTaskService().createTaskQuery()
                .taskIds(taskIds)
                .list()
                .stream()
                .collect(Collectors.toMap(
                        Task::getId,
                        Task::getTaskDefinitionKey
                ));

        // 3. 为每个对象设置节点Key
        approvalObjs.forEach(obj -> {
            String taskKey = taskKeyMap.get(obj.getTaskId());
            obj.setProcDefKey(taskKey);
        });

        return approvalObjs;
    }
    private List<ApprovalObj> determineSortOrder(List<ApprovalObj> approvalObjs,PendingApprovalQueryDto queryDto){
        String order = queryDto.getOrder() != null && POSITIVE_SORT.equalsIgnoreCase(queryDto.getOrder()) ? POSITIVE_SORT : REVERSE_SORT;

        approvalObjs = approvalObjs.stream()
                .sorted((o1, o2) -> {
                    LocalDateTime time1 = DateTimeUtils.getLocalDateTime(o1.getSubmitTime());
                    LocalDateTime time2 = DateTimeUtils.getLocalDateTime(o2.getSubmitTime());
                    if (POSITIVE_SORT.equals(order)) {
                        return time1.compareTo(time2);
                    } else {
                        return time2.compareTo(time1);
                    }
                })
                .collect(Collectors.toList());
        return approvalObjs;
    }

    @Override
    public PageVO<ApprovalObj> queryInitiatedList(InitiatedApprovalQueryDto queryDto) {
        String userId = authService.getUserId();
        List<ApprovalObj> approvalObjs = new ArrayList<>();
        List<String> userIds = Collections.singletonList(userId);
        Page<Object> page = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());
        approvalObjs = approvalRepository.queryInitiatedList(queryDto, userIds);
        log.info("approvalObjs: {}", approvalObjs);
//        if (!queryDto.isQueryConditionEmpty()) {
//            if (!approvalObjs.isEmpty()) {
//                List<String> approvalIds = approvalObjs.stream().map(ApprovalObj::getFlowId).collect(Collectors.toList());
//                processBo.setProcInsIds(approvalIds);
//            }
//        }
//        PageVO<TaskEntity> todoList = dcProcessService.ownList(processBo);
//        List<TaskEntity> taskEntityList = todoList.getList();
//        if (taskEntityList == null || taskEntityList.isEmpty()) {
//            return new PageVO<>(0, new ArrayList<>());
//        }
        //Map<String, String> insIdTaskIdMap = taskEntityList.stream().collect(Collectors.toMap(TaskEntity::getProcInsId, TaskEntity::getTaskId));
//        if (queryDto.isQueryConditionEmpty()) {
//            List<String> insIds = taskEntityList.stream().map(TaskEntity::getProcInsId).collect(Collectors.toList());
//            approvalObjs = approvalRepository.getByFlowIds(insIds);
//        }
        if (approvalObjs == null || approvalObjs.isEmpty()) {
            return new PageVO<>(0, new ArrayList<>());
        }
        List<String> submitUserIds = approvalObjs.stream().map(ApprovalObj::getSubmitUser).collect(Collectors.toList());
        List<UserVo> userinfoByIds = systemService.getUserinfoByIds(submitUserIds);
        Map<String, String> map = userinfoByIds.stream().collect(Collectors.toMap(UserVo::getId, UserVo::getDisplayText));
        approvalObjs.forEach(approvalObj -> {
            approvalObj.setSubmitUser(map.get(approvalObj.getSubmitUser()));
        });
        PageVO<ApprovalObj> pageVO = new PageVO<>(page.getTotal(), approvalObjs);
        return pageVO;
    }

    @Override
    public PageVO<ApprovalWithResultObj> queryCompletedList(CompletedApprovalQueryDto queryDto) {
        String userId = authService.getUserId();
        List<ApprovalWithResultObj> approvals = new ArrayList<>();
        PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());
        if (!queryDto.isQueryConditionEmpty()) {
            List<UserVo> userVos = systemService.getUserinfoBySearchText(queryDto.getSubmitUser());
            List<String> userIds = userVos.stream().map(UserVo::getId).collect(Collectors.toList());
            log.info("user ids: {}", userIds);
            approvals = approvalRepository.queryCompletedList(userId, queryDto, userIds);
        } else {
            approvals = approvalRepository.queryCompletedList(userId, queryDto, null);
        }
        if (CollectionUtils.isEmpty(approvals)){
            return new PageVO<>(0, new ArrayList<>());
        }
        assemblyUserDisplay(approvals);
        log.info("approvals: {}", approvals);
        PageInfo<ApprovalWithResultObj> pageInfo = new PageInfo<>(approvals);
        PageVO<ApprovalWithResultObj> pageVO = new PageVO<>(pageInfo.getTotal(), pageInfo.getList());
        pageVO.setPages(pageInfo.getPages());
        return pageVO;
    }
    //为审批任务列表设置用户的展示形式为姓名+工号
    private List<ApprovalWithResultObj>  assemblyUserDisplay(List<ApprovalWithResultObj> approvals) {
        List<String> submitUserIds = approvals.stream().map(ApprovalObj::getSubmitUser).collect(Collectors.toList());
        List<UserVo> userinfoByIds = systemService.getUserinfoByIds(submitUserIds);
        Map<String, String> map = userinfoByIds.stream().collect(Collectors.toMap(UserVo::getId, UserVo::getDisplayText));
        approvals.forEach(approvalObj -> {
            approvalObj.setSubmitUser(map.get(approvalObj.getSubmitUser()));
        });
        return approvals;
    }

    private List<ApprovalWithResultObj> getFlowableTaskList() {
        ProcessBo processBo=new ProcessBo();
        dcProcessService.finishedList(processBo);
        return null;
    }
    @Override
    public ApprovalVo getApprovalById(String approvalId) {
        ApprovalObj approvalObj = approvalRepository.getById(approvalId);
        log.info("approvalObj: {}", approvalObj);
        ApprovalVo approvalVo = ApprovalConvert.INSTANCE.convertObj2Vo(approvalObj);
        log.info("approvalVo: {}", approvalVo);
        List<Comment> commentList = new ArrayList<>();
        UserVo userInfo = systemService.getUserinfoById(approvalVo.getSubmitUser());
        if(userInfo!=null){
            approvalVo.setSubmitUser(userInfo.getDisplayText());
        }
        List<ProcessNodeDetailEntity> processNodeDetailEntities = dcProcessService.processNodeDetail(approvalObj.getFlowId());
        log.info("processNodeDetailEntities={}", processNodeDetailEntities);
        List<String> roleList=new ArrayList<>();
        processNodeDetailEntities.forEach(processNodeDetail -> {
            if (ObjectUtil.isNotNull(processNodeDetail.getCommentList()) && ObjectUtil.isNotEmpty(processNodeDetail.getCommentList())) {
                commentList.addAll(processNodeDetail.getCommentList());
                //收集角色名称
                roleList.add(processNodeDetail.getActivityName());
            }
        });
        List<String> userIds = commentList.stream().map(Comment::getUserId).collect(Collectors.toList());
        List<UserVo> userVos = systemService.getUserinfoByIds(userIds);
        Map<String, String> map = userVos.stream().collect(Collectors.toMap(UserVo::getId, UserVo::getDisplayText));
        List<CommentVo> comments = new ArrayList<>();
        commentList.forEach(comment -> {
            String id = comment.getId();
            String userId = comment.getUserId();
            String time = DateTimeUtils.getStringTime(comment.getTime());
            String type = comment.getType();
            Integer result = null;
            if (type.equals(FlowCommentEnum.NORMAL.getType())) {
                result = 1;
            } else if (type.equals(FlowCommentEnum.REJECT.getType())) {
                result = 0;
            }
            String fullMessage = comment.getFullMessage();
            String role= roleList.get(commentList.indexOf(comment))+" "+map.get(userId);
            comments.add(new CommentVo(id, role, time, result, fullMessage));
        });
        approvalVo.setComments(comments);
        return approvalVo;
    }

    @Override
    public ApprovalVo getApprovalByFlowId(String flowId) {
        ApprovalObj approvalObj = approvalRepository.getByFlowId(flowId);
        log.info("approvalObj: {}", approvalObj);
        ApprovalVo approvalVo = ApprovalConvert.INSTANCE.convertObj2Vo(approvalObj);
        return approvalVo;
    }

    @Override
    @Transactional
    public void submitApproval(ApprovalSubmitDto submitDto) {
        log.info("submitDto: {}", submitDto);
        TaskBo taskBo = new TaskBo();
        taskBo.setTaskId(submitDto.getTaskId());
        taskBo.setProcInsId(submitDto.getFlowId());
        taskBo.setResult(submitDto.getResult());
        taskBo.setComment(submitDto.getComment());
        //查询当前审批单
        ApprovalEntity approvalEntity = approvalRepository.queryByFlowId(submitDto.getFlowId());
        String bussesDataJson = approvalEntity.getBussesDataJson();
        List<MaterialToApprovalDto> source = JSON.parseArray(bussesDataJson, MaterialToApprovalDto.class);
        List<MaterialToApprovalDto> target = submitDto.getMaterials();
        //将提交的物料信息复制到数据库中保存的对象中
        Map<String, MaterialToApprovalDto> map = target.stream().collect(Collectors.toMap(MaterialToApprovalDto::getId, material1 -> material1, (oldValue, newValue) -> oldValue));
        for (MaterialToApprovalDto dto : source) {
            MaterialToApprovalDto approvalDto = map.get(dto.getId());
            if (approvalDto != null) {
                if (approvalDto.getCost() != null) {
                    dto.setCost(approvalDto.getCost());
                }
                if (approvalDto.getDeliveryDays() != null) {
                    dto.setDeliveryDays(approvalDto.getDeliveryDays());
                }
                if (approvalDto.getOthCode() != null) {
                    dto.setOthCode(approvalDto.getOthCode());
                }
            }
        }
        //更新数据库
        taskBo.setProcessBusinessKey(approvalEntity.getId());
        approvalRepository.updateBussesData(submitDto.getFlowId(), JSON.toJSONString(source));
        boolean complete = dcTaskService.complete(taskBo);
        log.info("complete: {}", complete);
        if (complete) {
            ApprovalObj newApproval = approvalRepository.getById(approvalEntity.getId());
            log.info("newApproval: {}", newApproval);
            if (newApproval.getStatus() != 0 && newApproval.getStatus() != 1) {
                ApprovalInformationDto approvalInfo = new ApprovalInformationDto();
                approvalInfo.setApprovalType(String.valueOf(newApproval.getApprovalType()));
                approvalInfo.setApprovalId(newApproval.getId());
                approvalInfo.setApprovalStatus(2);
                if (newApproval.getStatus().equals(ApprovalStatusEnum.APPROVED.getCode())) {
                    approvalInfo.setApprovalResult(ApprovalResult.PASS.getCode());
                } else if (newApproval.getStatus().equals(ApprovalStatusEnum.REJECTED.getCode())) {
                    approvalInfo.setApprovalResult(ApprovalResult.REJECT.getCode());
                }
                approvalInfo.setApprovalTime(DateTimeUtils.getCurrentTime());
                approvalInfo.setSubmitter(newApproval.getSubmitUser());
                List<MaterialDto> material = JSON.parseArray(newApproval.getBussesDataJson(), MaterialDto.class);
                approvalInfo.setMaterial(material);
                log.info("approvalInfo: {}", approvalInfo);
                productService.approvalUpdate(approvalInfo);
            }
        }
    }

    @Override
    @Transactional
    public void withdrawApproval(String flowId) {
        dcTaskService.stopProcess(flowId);
        ApprovalEntity approvalEntity = approvalRepository.queryByFlowId(flowId);
        productService.withdrawByApprovalId(approvalEntity.getId());
    }

    @Override
    /* Started by AICoder, pid:6b01fj4128a8fa514dd208c7a0f22d7856512f0b */
    public PageVO<?> queryMaterialsByApprovalId(ApprovalMaterialQueryDto queryDto) {
        String userId = authService.getUserId();
        log.info("queryMaterialsByApprovalId: {}", queryDto);
        ApprovalObj approvalObj = approvalRepository.getById(queryDto.getApprovalId());
        List<RoleVo> roleVos = systemService.queryRoleByUserIdAndResourceId(userId, approvalObj.getResourceId());
        Set<String> roleCodes = roleVos.stream()
                .map(RoleVo::getCode)
                .collect(Collectors.toSet());
        log.info("role codes: {}", roleCodes);

        String bussesDataJson = approvalObj.getBussesDataJson();
        log.info("bussesDataJson: {}", bussesDataJson);
        bussesDataJson=convertPurchaseMode(bussesDataJson);
        List<?> vos;
        String flowId = queryDto.getFlowId();
        String taskId = queryDto.getTaskId();

        if (!StringUtils.isEmpty(taskId)) {
            Task taskKey = dcTaskService.getTaskKey(flowId, taskId);
            String role = taskKey.getTaskDefinitionKey();
            log.debug("role:{}",role);
            vos = JSON.parseArray(bussesDataJson, ApprovalMaterialVo.class);
            if (RoleCodeEnum.COST_DIRECTOR.getCode().equals(role)) {
                log.info("need to fill cost");
                vos = JSON.parseArray(bussesDataJson, ApprovalMaterialWithCostVo.class);
            }
            if (RoleCodeEnum.LOGISTICS_DIRECTOR.getCode().equals(role)) {
                log.info("need to fill deliveryDays");
                vos = JSON.parseArray(bussesDataJson, ApprovalMaterialWithDeliveryDaysVo.class);
            }
            if (RoleCodeEnum.CONFIGURATION_MANAGER.getCode().equals(role)) {
                log.info("need to fill CONFIGURATION_MANAGER");
                vos = JSON.parseArray(bussesDataJson, ApprovalMaterialWithOthVo.class);
            }
        } else {
            vos = JSON.parseArray(bussesDataJson, ApprovalMaterialVo.class);
            if (roleCodes.contains(RoleCodeEnum.COST_DIRECTOR.getCode())) {
                log.info("need to fill cost");
                vos = JSON.parseArray(bussesDataJson, ApprovalMaterialWithCostVo.class);
            }
            if (roleCodes.contains(RoleCodeEnum.LOGISTICS_DIRECTOR.getCode())) {
                log.info("need to fill deliveryDays");
                vos = JSON.parseArray(bussesDataJson, ApprovalMaterialWithDeliveryDaysVo.class);
            }
            if (roleCodes.contains(RoleCodeEnum.CONFIGURATION_MANAGER.getCode())) {
                log.info("need to fill CONFIGURATION_MANAGER");
                vos = JSON.parseArray(bussesDataJson, ApprovalMaterialWithOthVo.class);
            }
            if (roleCodes.contains(RoleCodeEnum.COST_DIRECTOR.getCode()) &&
                    roleCodes.contains(RoleCodeEnum.LOGISTICS_DIRECTOR.getCode())) {
                log.info("return cost and deliveryDays");
                vos = JSON.parseArray(bussesDataJson, ApprovalMaterialAllVo.class);
            }
            if (roleCodes.contains(RoleCodeEnum.COST_DIRECTOR.getCode()) &&
                    roleCodes.contains(RoleCodeEnum.CONFIGURATION_MANAGER.getCode())) {
                log.info("return cost and deliveryDays");
                vos = JSON.parseArray(bussesDataJson, ApprovalMaterialAll1Vo.class);
            }
            if (roleCodes.contains(RoleCodeEnum.CONFIGURATION_MANAGER.getCode()) &&
                    roleCodes.contains(RoleCodeEnum.LOGISTICS_DIRECTOR.getCode())) {
                log.info("return cost and deliveryDays");
                vos = JSON.parseArray(bussesDataJson, ApprovalMaterialAll2Vo.class);
            }
            if (roleCodes.contains(RoleCodeEnum.COST_DIRECTOR.getCode()) &&
                    roleCodes.contains(RoleCodeEnum.CONFIGURATION_MANAGER.getCode()) &&
                    roleCodes.contains(RoleCodeEnum.LOGISTICS_DIRECTOR.getCode())) {
                log.info("return cost and CONFIGURATION_MANAGER");
                vos = JSON.parseArray(bussesDataJson, ApprovalMaterialOthVo.class);
            }
        }
        log.info("vos: {}", vos);
        int total = vos.size();
        int totalPages = (int) Math.ceil((double) total / queryDto.getPageSize());
        // 2025-01-10 前端要求返回全部数据
//        int fromIndex = (queryDto.getPageNum() - 1) * queryDto.getPageSize();
//        int toIndex = Math.min(fromIndex + queryDto.getPageSize(), total);
//        List<?> subList = vos.subList(fromIndex, toIndex);
        PageVO<?> pageVO = new PageVO<>(total, vos);
        pageVO.setPages(totalPages);
        return pageVO;
    }

    /* Ended by AICoder, pid:6b01fj4128a8fa514dd208c7a0f22d7856512f0b */

    @Override
    @Transactional
    public String createApproval(NewApprovalDto newApprovalDto) {
        log.info("newApprovalDto: {}", newApprovalDto);
        checkUserByproductCategory(newApprovalDto.getProductCategory(),newApprovalDto.getApproval18Code(),newApprovalDto.getApprovalOth());
        String userId = authService.getUserId();
        HashMap<String, Object> map = new HashMap<>();
        map.put(ProcessConstants.PRODUCT_CATEGORY, newApprovalDto.getProductCategory());
        map.put("approvalOth",newApprovalDto.getApprovalOth());
        map.put(ProcessConstants.APPROVAL_18_CODE,newApprovalDto.getApproval18Code());
        String id = UUID.randomUUID().toString();
        String title = "";
        List<MaterialToApprovalDto> materials = newApprovalDto.getMaterials();
        if (materials.size() == 1) {
            title = "物料审批，物料名称：" + materials.get(0).getName() + "，请审批！";
        } else {
            title = "批量物料审批，本次审批涉及物料数量" + materials.size() + "个，请审批！";
        }
        ApprovalEntity approvalEntity = new ApprovalEntity();
        approvalEntity.setId(id);
        approvalEntity.setTitle(title);
        approvalEntity.setApprovalType(newApprovalDto.getType());
        approvalEntity.setSubmitUser(userId);
        approvalEntity.setSubmitTime(DateTimeUtils.getCurrentTime());
        approvalEntity.setStatus(ApprovalStatusEnum.PENDING.getCode());
        approvalEntity.setResourceId(newApprovalDto.getProductCategory());
        approvalEntity.setCreateTime(DateTimeUtils.getCurrentTime());
        approvalEntity.setUpdateTime(DateTimeUtils.getCurrentTime());
        approvalEntity.setCreateBy(userId);
        approvalEntity.setUpdateBy(userId);
        approvalEntity.setBussesDataJson(JSON.toJSONString(materials));
        approvalRepository.createApproval(approvalEntity);

        //todo put logic to commons
        String processKey = ProcessEnum.MATERIAL_SHELF_PROCESS.getProcessKey();
        if (String.valueOf(newApprovalDto.getType()).equals(ProcessEnum.MATERIAL_CHANGE.getType())) {
            processKey = ProcessEnum.MATERIAL_CHANGE.getProcessKey();
        } else if (String.valueOf(newApprovalDto.getType()).equals(ProcessEnum.MATERIAL_UN_SHELF.getType())) {
            processKey = ProcessEnum.MATERIAL_UN_SHELF.getProcessKey();
        }
        String flowId = dcProcessService.startProcessByKey(processKey, id, map);
        approvalRepository.updateFlowIdById(id, flowId);

        return id;
    }


    /**
     * "查询'市场投标支持'详情"
     * @param flowId 任务流程ID
     * */
    @Override
    public MktApprovalDetailVo getMktApprovalDetail(String flowId) {
        ApprovalObj approvalObj = approvalRepository.getByFlowId(flowId);
        if (approvalObj==null){
            log.error("Failed to query market bid support details by task id，flowId:{}",flowId);
            throw new BusinessException(ProcessStatusCode.PROCESS_DEFINITION_NOT_FOUND);
        }
        MktApprovalDetailVo detailVo=new MktApprovalDetailVo();
        //调用RPC根据项目id查询项目信息(当前为"市场投标支持"这里的bussesDataJson存储的就是项目id)
        ProjectDetailInfoVo detailInfoVo=projectService.getProjectDetailInfo(jsonStrToMap(approvalObj.getBussesDataJson()));
        //项目id
        detailVo.setId(detailInfoVo.getId());
        //项目名称
        detailVo.setProjectName(detailInfoVo.getName());
        //地区
        detailVo.setAreaPath(detailInfoVo.getAreaName());
        //当前流程的方案SE
        UserVo userInfo = systemService.getUserinfoById(approvalObj.getSubmitUser());
        if(userInfo!=null){
            detailVo.setSchemeSe(userInfo.getDisplayText());
        }
        return detailVo;
    }

    @Override
    public ApprovalVo selectFlowIdByCategoryIdAndProjectId(String projectId, String productCategoryId) {
        ApprovalObj obj=approvalRepository.selectFlowIdByCategoryIdAndProjectId(projectId,productCategoryId);
        if (obj==null){
            //为了避免脏数据干扰 这里直接返回
            return null;
//            log.error("Failed to query approval flowId by project id and product category id，projectId:{},bussesDataJson:{}",projectId,productCategoryId);
//            throw new BusinessException(StatusCode.DATA_NOT_FOUND);
        }
        return ApprovalConvert.INSTANCE.convertObj2Vo(obj);
    }

    @Override
    public boolean updateApprovalInfo(ApprovalEntity approvalEntity) {
        return approvalRepository.updateApprovalStatus(approvalEntity);
    }

    @Override
    public List<ApprovalCommonVo> getApprovalByProjectId(String projectId) {
        List<ApprovalObj> approvalObjs = approvalRepository.queryApprovalByProjectId(projectId);
        return ApprovalConvert.INSTANCE.convertObj2CommVo(approvalObjs);
    }

    /* Started by AICoder, pid:s72c0pe06d37ebb1422a09107058704464a39121 */
    @Override
    public ApprovalCommonVo getApprovalByApprovalId(String approvalId) {
        // 从数据库中根据审批ID获取审批对象
        ApprovalObj approvalObj = approvalRepository.getById(approvalId);

        // 如果审批对象不存在，返回 null
        if (approvalObj == null) {
            return null;
        }

        // 创建 ApprovalCommonVo 对象
        ApprovalCommonVo approvalCommonVo = new ApprovalCommonVo();

        // 设置 ApprovalCommonVo 的各个属性
        approvalCommonVo.setId(approvalObj.getId());
        approvalCommonVo.setTitle(approvalObj.getTitle());
        approvalCommonVo.setApprovalType(approvalObj.getApprovalType());
        approvalCommonVo.setResourceId(approvalObj.getResourceId());
        approvalCommonVo.setSubmitUser(approvalObj.getSubmitUser());
        approvalCommonVo.setSubmitTime(approvalObj.getSubmitTime());
        approvalCommonVo.setFlowId(approvalObj.getFlowId());
        approvalCommonVo.setTaskId(approvalObj.getTaskId());
        approvalCommonVo.setStatus(approvalObj.getStatus());
        approvalCommonVo.setCreateTime(approvalObj.getCreateTime());
        approvalCommonVo.setUpdateTime(approvalObj.getUpdateTime());
        approvalCommonVo.setCreateBy(approvalObj.getCreateBy());
        approvalCommonVo.setUpdateBy(approvalObj.getUpdateBy());
        approvalCommonVo.setBussesDataJson(approvalObj.getBussesDataJson());
        approvalCommonVo.setProcDefKey(approvalObj.getProcDefKey());
        approvalCommonVo.setStaticTaskId(approvalObj.getStaticTaskId());
        approvalCommonVo.setBillQuantityId(approvalObj.getBillQuantityId());
        approvalCommonVo.setBillQuantityPerson(approvalObj.getBillQuantityPerson());
        approvalCommonVo.setProjectId(approvalObj.getProjectId());

        // 返回包含审批信息的 ApprovalCommonVo 对象
        return approvalCommonVo;
    }

    @Override
    public ApprovalCommonVo queryApprovalByLectotypeId(String lectotypeId) {
        ApprovalObj approvalObj = approvalRepository.queryApprovalByLectotypeId(lectotypeId);
        return ApprovalConvert.INSTANCE.singleConvertObj2CommVo(approvalObj);
    }

    @Override
    public List<ApprovalCommonVo> queryApprovalByLectotypeIdList(List<String> lectotypeIdList) {
        List<ApprovalObj> approvalObjs = approvalRepository.queryApprovalByLectotypeIdList(lectotypeIdList);
        return ApprovalConvert.INSTANCE.convertObj2CommVo(approvalObjs);
    }
    /* Ended by AICoder, pid:s72c0pe06d37ebb1422a09107058704464a39121 */

    /* Started by AICoder, pid:qd043m512di127c147f50aeb508d742fb803ad35 */
    /**
     * 检查指定产品类别中是否设置了必要的用户角色。

     * @param productCategoryId 产品类别的ID。
     * @param approval18Code 0-是18销售代码。
     * @throws BusinessException 当任何必要角色未设置时抛出。
     */
    private void checkUserByproductCategory(String productCategoryId,Integer approval18Code,Integer approvalOth) {
        // 检查产品经理是否设置
        verifyRoleAssigned(RoleCodeEnum.PRODUCT_MANAGER, productCategoryId, ProcessRoleStatusCode.PROCESS_PRODUCT_MANAGER_NOT_FOUND);
        //18代码只需要配置产品经理
        if (approval18Code != null && approval18Code == 0 ) {
            return;
        }
        // 检查成本总监是否设置
        verifyRoleAssigned(RoleCodeEnum.COST_DIRECTOR, productCategoryId, ProcessRoleStatusCode.PROCESS_COST_DIRECTOR_NOT_FOUND);
        // 如果approvalOth为0 不申请OTH，则不需要检查配置经理
        if (approvalOth != null && approvalOth == 0) {
            return;
        }
        // 申请OTH时需要配置经理
        // 检查配置经理（物流经理）是否设置
        verifyRoleAssigned(RoleCodeEnum.CONFIGURATION_MANAGER, productCategoryId, ProcessRoleStatusCode.PROCESS_CONFIGURATION_MANAGER_NOT_FOUND);
    }
    /* Ended by AICoder, pid:qd043m512di127c147f50aeb508d742fb803ad35 */
    /**
     * 验证指定角色是否已分配给某个产品类别。
     * @param roleCodeEnum 角色编码枚举
     * @param productCategoryId 产品类别ID
     * @param statusCode 当未找到对应角色时抛出的业务异常状态码
     */
    private void verifyRoleAssigned(RoleCodeEnum roleCodeEnum, String productCategoryId, ProcessRoleStatusCode statusCode) {
        List<UserVo> userList = systemService.getUserByRoleCodeAndResourceId(roleCodeEnum.getCode(), productCategoryId);
        if (userList.isEmpty()) {
            throw new BusinessException(statusCode);
        }
    }

    /* Started by AICoder, pid:b1d95434c0tcf001491c0989504b66382bc7828c */
    /**
     * 用于转换 JSON 字符串中 purchaseMode 字段的工具方法。
     *
     * @param bussesDataJson 包含 ApprovalMaterialVo 对象的 JSON 字符串
     * @return 转换后的 JSON 字符串，其中 purchaseMode 字段被替换为对应的国际化字符串
     */
    public String convertPurchaseMode(String bussesDataJson) {
        List<ApprovalMaterialOthVo> materialVos;
        try {
            materialVos = JSON.parseArray(bussesDataJson, ApprovalMaterialOthVo.class);
            for (ApprovalMaterialVo item : materialVos) {
                // 根据 purchaseMode 的值获取对应的枚举对象
                PurchaseModeEnums modeEnum = PurchaseModeEnums.getById(item.getPurchaseMode());
                if (modeEnum != null) {
                    String purchaseMode = I18nUtil.getI18nFromString(modeEnum.getName());
                    item.setPurchaseMode(purchaseMode);
                } else {
                    log.error("Unknown purchaseMode: " + item.getPurchaseMode());
                }
            }
            return JSON.toJSONString(materialVos);
        } catch (BusinessException e) {
            log.error("bussesDataJson failed to transfer an ApprovalMaterialVo", e);
        }
        // 如果发生异常，返回原始 JSON 字符串
        return bussesDataJson;
    }

    /**
     *  将 JSON 字符串转换为 Map 对象，并从 Map 中获取 projectId 的值。
     * */
    private String jsonStrToMap(String bussesDataJson) {
        try {
            List<Map<String, String>> projectIdList = mapper.readValue(bussesDataJson, List.class);

            // 从List中获取第一个Map，并从中获取projectId的值
            if (CollectionUtils.isNotEmpty(projectIdList)&&projectIdList.size() == 1) {
                Map<String, String> firstElement = projectIdList.get(0);
                return firstElement.get(TaskConstants.PROJECT_ID);
            }
            log.error("bussesDataJson failed to transfer an ApprovalMaterialVo");
            throw new BusinessException(StatusCode.PARAM_ERROR);
        } catch (JsonProcessingException e) {
            log.error("bussesDataJson failed to transfer an ApprovalMaterialVo", e);
            throw new BusinessException(StatusCode.PARAM_ERROR);
        }
    }
    /* Ended by AICoder, pid:b1d95434c0tcf001491c0989504b66382bc7828c */
}
