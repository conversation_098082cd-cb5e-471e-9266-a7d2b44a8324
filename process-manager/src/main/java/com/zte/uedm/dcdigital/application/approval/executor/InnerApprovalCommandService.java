/* Started by AICoder, pid:72e7e8a9204250114d070a6640e8861377c6dfe5 */
package com.zte.uedm.dcdigital.application.approval.executor;

import com.zte.uedm.dcdigital.common.bean.process.TaskDataInnerVo;
import java.util.List;

/**
 * 内部审批命令服务接口
 */
public interface InnerApprovalCommandService {
    /**
     * 查询部分任务统计数据
     *
     * @return 任务统计列表，可能返回空列表但不会返回null
     */
    List<TaskDataInnerVo> queryTaskStatistics();
}

/* Ended by AICoder, pid:72e7e8a9204250114d070a6640e8861377c6dfe5 */