package com.zte.uedm.dcdigital.interfaces.web.controller;

import com.zte.uedm.dcdigital.application.approval.executor.ApprovalCommandService;
import com.zte.uedm.dcdigital.application.approval.executor.ApprovalQueryService;
import com.zte.uedm.dcdigital.application.approval.executor.InnerApprovalCommandService;
import com.zte.uedm.dcdigital.application.approval.executor.MktApprovalCommandService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.process.MktApprovalAddDto;
import com.zte.uedm.dcdigital.common.bean.process.MktApprovalCancelDto;
import com.zte.uedm.dcdigital.common.bean.process.TaskDataInnerVo;
import com.zte.uedm.dcdigital.common.util.ValidResult;
import com.zte.uedm.dcdigital.common.util.ValidateUtils;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.domain.model.approval.ApprovalObj;
import com.zte.uedm.dcdigital.domain.model.approval.ApprovalWithResultObj;
import com.zte.uedm.dcdigital.domain.service.DemandProcessService;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ApprovalMapper;
import com.zte.uedm.dcdigital.interfaces.web.dto.*;
import com.zte.uedm.dcdigital.interfaces.web.vo.ApprovalVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.MktApprovalDetailVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.ProjectTaskInfoVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.TaskDetailVo;
import com.zte.uedm.dcdigital.log.annotation.DcOperationLog;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.List;

@Path("uportal/station/approval")
@Controller
public class ApprovalUPortalController {

    @Autowired
    private ApprovalCommandService approvalCommandService;

    @Autowired
    private MktApprovalCommandService mktApprovalCommandService;

    @Autowired
    private ApprovalQueryService approvalQueryService;

    @Autowired
    private DemandProcessService demandProcessService;

    @Autowired
    private InnerApprovalCommandService innerApprovalCommandService;

    @POST
    @Path("/pending")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "查询待我处理评审单", notes = "查询待我处理评审单", httpMethod = "POST")
    public BaseResult<PageVO<ApprovalObj>> queryPendingList(PendingApprovalQueryDto queryDto){
        //PageVO<ApprovalObj> pageVO = approvalQueryService.queryPendingList(queryDto);
        ProcessPendingDto dto = new ProcessPendingDto();
        dto.setTitle(queryDto.getTitle());
        dto.setUserId(queryDto.getSubmitUser());
        dto.setApprovalType(queryDto.getType());
        dto.setOrder(queryDto.getOrder());
        dto.setSort(queryDto.getSort());
        dto.setStartTime(queryDto.getStartTime());
        dto.setEndTime(queryDto.getEndTime());
        dto.setPageSize(queryDto.getPageSize());
        dto.setPageNum(queryDto.getPageNum());
        return BaseResult.success(demandProcessService.queryPendingList(dto));
    }

    @POST
    @Path("/initiated")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "查询我发起的评审单", notes = "查询我发起的评审单", httpMethod = "POST")
    public BaseResult<PageVO<ApprovalObj>> queryInitiatedList(InitiatedApprovalQueryDto queryDto){
        PageVO<ApprovalObj> pageVO = approvalQueryService.queryInitiatedList(queryDto);
        return BaseResult.success(pageVO);
    }

    @POST
    @Path("/completed")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "查询我已处理评审单", notes = "创建评审单", httpMethod = "POST")
    public BaseResult<PageVO<ApprovalWithResultObj>> queryCompletedList(CompletedApprovalQueryDto queryDto){
        PageVO<ApprovalWithResultObj> pageVO = approvalQueryService.queryCompletedList(queryDto);
        return BaseResult.success(pageVO);
    }

    @GET
    @Path("/detail/{approvalId}")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据ID查询评审单详情", notes = "根据ID查询评审单详情", httpMethod = "POST")
    public BaseResult<ApprovalVo> getApprovalById(@PathParam("approvalId") String approvalId){
        ApprovalVo approval = approvalQueryService.getApprovalById(approvalId);
        return BaseResult.success(approval);
    }

    @POST
    @Path("/mktDetail")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据approvalId,flowId,staticTaskId查询市场投标支持评审单详情", notes = "根据approvalId,flowId,staticTaskId查询市场投标支持评审单详情", httpMethod = "POST")
    public BaseResult<ApprovalVo> getMktApprovalById(MktHistoryQueryDto queryDto){
        ApprovalVo approval = approvalQueryService.getMktFlowDataHistory(queryDto);
        return BaseResult.success(approval);
    }

    @POST
    @Path("/detail/materials")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "查询评审单中的物料信息", notes = "查询评审单中的物料信息", httpMethod = "POST")
    public BaseResult<PageVO<?>> queryMaterialsByApprovalId(ApprovalMaterialQueryDto queryDto){
        PageVO<?> pageVO = approvalQueryService.queryMaterialsByApprovalId(queryDto);
        return BaseResult.success(pageVO);
    }

    @POST
    @Path("/submit")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @DcOperationLog(method = OperationMethodEnum.ADD, module = "module-approval-manager",
            targetClass = ApprovalSubmitDto.class, operation = "ApprovalAdd",mapperName= ApprovalMapper.class)
    @ApiOperation(value = "提交评审单", notes = "提交评审单", httpMethod = "POST")
    public BaseResult<Object> submitApproval(ApprovalSubmitDto submitDto){
        approvalCommandService.submitApproval(submitDto);
        return BaseResult.success();
    }

    @POST
    @Path("/withdraw")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @DcOperationLog(method = OperationMethodEnum.UPDATE, module = "module-approval-manager",
            targetClass = ApprovalSubmitDto.class, operation = "ApprovalEditDescription" ,mapperName= ApprovalMapper.class)
    @ApiOperation(value = "撤回评审单", notes = "撤回评审单", httpMethod = "POST")
    public BaseResult<Object> withdrawApproval(@QueryParam("flowId") String flowId){
        approvalCommandService.withdrawApproval(flowId);
        return BaseResult.success();
    }

    @POST
    @Path("/accept")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "处理评审单", notes = "处理评审单", httpMethod = "POST")
    public BaseResult<Object> acceptMktApproval(MktApprovalSubmitDto mktApprovalSubmitDto){
        mktApprovalCommandService.acceptMktApproval(mktApprovalSubmitDto);
        return BaseResult.success();
    }
    @GET
    @Path("/task-detail")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "查询'市场投标支持'详情", notes = "查询'市场投标支持'详情", httpMethod = "GET")
    public BaseResult<MktApprovalDetailVo> getMktApprovalDetail(@QueryParam("flowId") String flowId){
        MktApprovalDetailVo vo=approvalQueryService.getMktApprovalDetail(flowId);
        return BaseResult.success(vo);
    }


    @POST
    @Path("/create-mkt-approval")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @DcOperationLog(method = OperationMethodEnum.ADD, module = "module-approval-manager",
            targetClass = MktApprovalAddDto.class, operation = "MktApprovalAdd",mapperName= ApprovalMapper.class)
    @ApiOperation(value = "创建'市场投标支持' 评审单", notes = "创建'市场投标支持' 评审单", httpMethod = "POST")
    public BaseResult<String> createMktApproval(MktApprovalAddDto approvalDto) {
        String id = mktApprovalCommandService.createMarketBidApproval(approvalDto);
        return BaseResult.success(id);
    }

    @POST
    @Path("/cancel-mkt-approval")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "撤销'市场投标支持' 评审单", notes = "撤销'市场投标支持' 评审单", httpMethod = "POST")
    public BaseResult<String> cancelMktApproval(MktApprovalCancelDto cancelDto) {
        mktApprovalCommandService.cancelMarketBidApproval(cancelDto);
        return BaseResult.success();
    }

    /* Started by AICoder, pid:k6563ub19d9235e140b709fe007bb0493153b93c */
    @POST
    @Path("/task-details")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "任务详情", notes = "任务详情", httpMethod = "POST")
    public BaseResult<PageVO<TaskDetailVo>> queryTaskDetails(TaskDetailQueryDto queryDto){
        ValidResult validResult = ValidateUtils.validateObj(queryDto);
        if (validResult.isError()) {
            return BaseResult.paramError(validResult.getErrorMessage());
        }
        PageVO<TaskDetailVo> pageVO = approvalQueryService.queryTaskDetails(queryDto);
        return BaseResult.success(pageVO);
    }
    @GET
    @Path("/task-overview")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "任务概览", notes = "任务概览", httpMethod = "GET")
    public BaseResult<TaskDetailVo> queryTaskOverview(@QueryParam("projectId") String projectId){
        if (StringUtils.isBlank(projectId)) {
            return BaseResult.failed("projectId can not be empty.");
        }
        TaskDetailVo result = approvalQueryService.queryTaskOverview(projectId);
        return BaseResult.success(result);
    }

    @POST
    @Path("/tasks")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "任务列表", notes = "任务列表", httpMethod = "POST")
    public BaseResult<List<ApprovalVo>> queryTaskList(TaskQueryDto queryDto){
        ValidResult validResult = ValidateUtils.validateObj(queryDto);
        if (validResult.isError()) {
            return BaseResult.paramError(validResult.getErrorMessage());
        }
        List<ApprovalVo> result = approvalQueryService.queryTaskList(queryDto);
        return BaseResult.success(result);
    }
    /* Ended by AICoder, pid:k6563ub19d9235e140b709fe007bb0493153b93c */

    @POST
    @Path("/project/task-details")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "任务详情", notes = "任务详情", httpMethod = "POST")
    public BaseResult<PageVO<ProjectTaskInfoVo>> queryTaskDetailsByType(TaskTypeDetailQueryDto queryDto){
        ValidResult validResult = ValidateUtils.validateObj(queryDto);
        if (validResult.isError()) {
            return BaseResult.paramError(validResult.getErrorMessage());
        }
        PageVO<ProjectTaskInfoVo> pageVO = approvalQueryService.queryTaskByType(queryDto);
        return BaseResult.success(pageVO);
    }

    @POST
    @Path("/query-task-statistics")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "查询部分任务统计数据", notes = "3投标支持任务+1产品提资任务+1招标申请", httpMethod = "POST")
    public BaseResult<List<TaskDataInnerVo>> queryTaskStatistics()
    {
        List<TaskDataInnerVo> taskDataInnerVo = innerApprovalCommandService.queryTaskStatistics();
        return BaseResult.success(taskDataInnerVo);
    }
}
