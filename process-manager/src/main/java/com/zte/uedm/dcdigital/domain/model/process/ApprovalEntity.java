package com.zte.uedm.dcdigital.domain.model.process;

/* Started by AICoder, pid:z41cba21d7ge08a1485b0b79f0f5a36083f8defa */
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ApprovalEntity {
    /**
     * 唯一标识
     */
    private String id;

    /**
     * 审批名称
     */
    private String title;

    /**
     * 审批类型,1 物料上架、2 物料变更、3 物料下架、4市场投标支持、5 标书澄清、6 物料选型、 7 文档编写、8 招标申请、9 产品提资
     */
    private Integer approvalType;

    /**
     * 发起人
     */
    private String submitUser;

    /**
     * 提交时间
     */
    private String submitTime;

    /**
     * 流程实例ID
     */
    private String flowId;

    /**
     * 审批状态,0-待审批，1-审批中，2-审批通过，3-审批不通过，4-已撤回，5-待处理，6-处理中，7-待验收，8-已验收，9-已终止
     */
    private Integer status;

    /**
     * 资源ID(目前有产品小类、物料)
     */
    private String resourceId;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 业务数据
     */
    private String bussesDataJson;

    /**
     * 工程量清单id
     */
    private String billQuantityId;

    /**
     * 工程量清单流程任务负责人
     */
    private String billQuantityPerson;

    /**
     * 实时处理时间
     */
    private String handlerTime;

    /**
     * 项目id/商机id
     */
    private String projectId;
}
/* Ended by AICoder, pid:z41cba21d7ge08a1485b0b79f0f5a36083f8defa */
