package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.domain.common.enums.ApprovalStatusEnum;
import com.zte.uedm.dcdigital.domain.common.enums.ProcessEnum;
import com.zte.uedm.dcdigital.domain.model.approval.ApprovalObj;
import com.zte.uedm.dcdigital.domain.model.approval.ApprovalWithResultObj;
import com.zte.uedm.dcdigital.domain.repository.ApprovalRepository;
import com.zte.uedm.dcdigital.domain.model.process.ApprovalEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.ApprovalConvert;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ApprovalMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ApprovalPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.CompletedApprovalQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.InitiatedApprovalQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.PendingApprovalQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProcessPendingDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.ApprovalVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.TaskDetailVo;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;

@Repository
public class ApprovalRepositoryImpl extends ServiceImpl<ApprovalMapper, ApprovalPo> implements ApprovalRepository {

    private static final int bidClearType=Integer.parseInt(ProcessEnum.BID_CLARIFICATION.getType());
    private static final int materialType=Integer.parseInt(ProcessEnum.MATERIAL_SELECTION.getType());
    private static final int documentType=Integer.parseInt(ProcessEnum.DOCUMENTATION.getType());
    @Autowired
    private ApprovalMapper approvalMapper;
    @Resource
    private AuthService authService;

    /* Started by AICoder, pid:lf14ejce6didb901423c0987e00b851e41b18134 */
    /**
     * 根据流程实例ID查询审批实体。
     *
     * @param flowId 流程实例ID，用于标识特定的审批流程实例。
     * @return 返回与指定流程实例ID匹配的审批实体。如果没有找到匹配项，则返回null。
     */
    @Override
    public ApprovalEntity queryByFlowId(String flowId) {
        ApprovalPo approvalPo = approvalMapper.queryByFlowId(flowId);
        return ApprovalConvert.INSTANCE.approvalPoToEntity(approvalPo);
    }

    @Override
    public List<ApprovalEntity> queryApprovalListByFlowIds(List<String> flowIds) {
        List<ApprovalPo> approvalPo = approvalMapper.queryByFlowIds(flowIds);
        return ApprovalConvert.INSTANCE.approvalPoToListEntity(approvalPo);
    }
    /* Ended by AICoder, pid:lf14ejce6didb901423c0987e00b851e41b18134 */

    /* Started by AICoder, pid:od342se13c128131428f0836004cf31b38927b3e */
    /**
     * 更新审批实体的审批状态。
     *
     * @param approvalEntity 包含更新后审批状态的审批实体。
     * @return 如果更新成功，返回 `true`；否则返回 `false`。
     */
    @Override
    public boolean updateApprovalStatus(ApprovalEntity approvalEntity) {
        // 获取更新人
        String userId = authService.getUserId();
        approvalEntity.setUpdateBy(userId);
        String currentTime = DateTimeUtils.getCurrentTime();
        approvalEntity.setUpdateTime(currentTime);
        approvalEntity.setHandlerTime(currentTime);
        ApprovalPo approvalPo = ApprovalConvert.INSTANCE.approvalEntityToPo(approvalEntity);
        int result = this.baseMapper.updateById(approvalPo);
        return result > 0;
    }
    /* Ended by AICoder, pid:od342se13c128131428f0836004cf31b38927b3e */

    @Override
    public List<ApprovalObj> queryPendingList(PendingApprovalQueryDto queryDto, List<String> userIds) {
        List<ApprovalPo> approvalPos = approvalMapper.queryPendingList(queryDto, userIds);
        return ApprovalConvert.INSTANCE.convertPos2Objs(approvalPos);
    }

    @Override
    public List<ApprovalObj> queryPendingListNew( ProcessPendingDto processBo) {
        List<ApprovalPo> approvalPos = approvalMapper.queryPendingListNew(processBo);
        return ApprovalConvert.INSTANCE.convertPos2Objs(approvalPos);
    }


    @Override
    public List<ApprovalObj> queryInitiatedList(InitiatedApprovalQueryDto queryDto, List<String> userIds) {
        List<ApprovalPo> approvalPos = approvalMapper.queryInitiatedList(queryDto, userIds);
        return ApprovalConvert.INSTANCE.convertPos2Objs(approvalPos);
    }

    @Override
    public List<ApprovalWithResultObj> queryCompletedList(String userId, CompletedApprovalQueryDto queryDto, List<String> userIds) {
        return approvalMapper.queryCompletedList(userId, queryDto, userIds);
    }

    @Override
    public ApprovalObj getById(String id) {
        ApprovalPo approvalPo = approvalMapper.selectById(id);
        return ApprovalConvert.INSTANCE.convertPo2Obj(approvalPo);
    }

    @Override
    public List<ApprovalObj> getByFlowIds(List<String> ids) {
        List<ApprovalPo> approvalPos = approvalMapper.queryByFlowIds(ids);
        return ApprovalConvert.INSTANCE.convertPos2Objs(approvalPos);
    }

    @Override
    public void updateBussesData(String flowId, String data) {
        approvalMapper.updateBussesDataJson(flowId, data);
    }

    @Override
    public void createApproval(ApprovalEntity approvalEntity) {
        ApprovalPo approvalPo = ApprovalConvert.INSTANCE.approvalEntityToPo(approvalEntity);
        this.baseMapper.insert(approvalPo);
    }

    @Override
    public void updateFlowIdById(String id, String flowId) {
        approvalMapper.updateFlowIdById(id, flowId);
    }

    @Override
    public ApprovalObj getByFlowId(String flowId) {
        LambdaQueryWrapper<ApprovalPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ApprovalPo::getFlowId, flowId);
        queryWrapper.ne(ApprovalPo::getApprovalType, 4);
        ApprovalPo approvalPo=approvalMapper.selectOne(queryWrapper);
        return ApprovalConvert.INSTANCE.convertPo2Obj(approvalPo);
    }

    @Override
    public ApprovalObj selectFlowIdByCategoryIdAndProjectId(String projectId, String productCategoryId) {
        LambdaQueryWrapper<ApprovalPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(ApprovalPo::getStatus, ApprovalStatusEnum.WITHDRAWN.getCode());
        queryWrapper.eq(ApprovalPo::getBussesDataJson, projectId);
        queryWrapper.eq(ApprovalPo::getResourceId, productCategoryId);
        queryWrapper.orderByDesc(ApprovalPo::getCreateTime);
        // 使用last方法限制返回结果数量为1，获取最新的那条记录
        queryWrapper.last("LIMIT 1");
        ApprovalPo approvalPo = approvalMapper.selectOne(queryWrapper);
        return ApprovalConvert.INSTANCE.convertPo2Obj(approvalPo);
    }

    /* Started by AICoder, pid:6c7dfy62e2ga62f14d020ae7a0c2335c0a242860 */
    @Override
    public PageVO<TaskDetailVo> queryTaskDetails(String bussesDataJson, Integer pageNum, Integer pageSize) {

        PageHelper.startPage(pageNum, pageSize);
        List<TaskDetailVo> poList = approvalMapper.calculateTasks(bussesDataJson);
        PageInfo<TaskDetailVo> pageInfo = new PageInfo<>(poList);
        return new PageVO<>(pageInfo.getTotal(), poList);
    }

    @Override
    public PageVO<TaskDetailVo> queryTaskDetailsByProjectId(String projectId, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<TaskDetailVo> poList = approvalMapper.calculateByProjectId(projectId);
        PageInfo<TaskDetailVo> pageInfo = new PageInfo<>(poList);
        return new PageVO<>(pageInfo.getTotal(), poList);
    }

    @Override
    public List<ApprovalObj> queryTaskByResourceId(String bussesDataJson) {
        LambdaQueryWrapper<ApprovalPo> wrapper = Wrappers.<ApprovalPo>lambdaQuery().eq(ApprovalPo::getBussesDataJson, bussesDataJson);
        //排除已撤回的
        wrapper.ne(ApprovalPo::getStatus, ApprovalStatusEnum.WITHDRAWN.getCode())
                .eq(ApprovalPo::getApprovalType, 4);//市场投标支持
        List<ApprovalPo> poList = this.list(wrapper);
        return ApprovalConvert.INSTANCE.convertPos2Objs(poList);
    }

//    @Override
//    public List<ApprovalObj> queryApprovalByProjectId(String projectId) {
//        LambdaQueryWrapper<ApprovalPo> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(ApprovalPo::getProjectId, projectId);
//        List<ApprovalPo> pos = this.list(wrapper);
//        return ApprovalConvert.INSTANCE.convertPos2Objs(pos);
//    }

/* Started by AICoder, pid:k84bf44bc6v088114da008632020ba3740b2dc94 */
    @Override
    public List<ApprovalObj> queryApprovalByProjectId(String projectId) {
        // 1. 基础查询
        List<ApprovalPo> allData = this.lambdaQuery()
                .eq(ApprovalPo::getProjectId, projectId)
                .ne(ApprovalPo::getApprovalType,4)
                .orderByDesc(ApprovalPo::getSubmitTime)
                .list();

        // 2. 内存处理过滤特殊流程
        Map<String, ApprovalPo> specialFlowMap = new HashMap<>();
        List<ApprovalPo> normalData = new ArrayList<>();
        for (ApprovalPo po : allData) {
            if (Arrays.asList(bidClearType, materialType, documentType).contains(po.getApprovalType())) {
                // 构造唯一键
                String key = po.getApprovalType() + "_" + po.getResourceId() + "_" + po.getProjectId();
                // 保留最新记录（因已按时间倒序，第一个遇到的即最新）
                specialFlowMap.putIfAbsent(key, po);
            } else {
                normalData.add(po);
            }
        }

        // 3. 合并结果
        List<ApprovalPo> filteredData = new ArrayList<>(specialFlowMap.values());
        filteredData.addAll(normalData);

        // 4. 按需重新排序
        filteredData.sort(Comparator.comparing(ApprovalPo::getSubmitTime).reversed());

        return ApprovalConvert.INSTANCE.convertPos2Objs(filteredData);
    }

    /* Ended by AICoder, pid:k84bf44bc6v088114da008632020ba3740b2dc94 */

    @Override
    public PageVO<ApprovalObj> queryApprovalByProjectIdAndType(String projectId, Integer type, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
//        LambdaQueryWrapper<ApprovalPo> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(ApprovalPo::getProjectId, projectId)
//                .eq(ApprovalPo::getApprovalType, type);
//        List<ApprovalPo> pos = this.list(wrapper);
        List<ApprovalPo> pos = approvalMapper.queryApprovalByProjectIdAndType(projectId, type);
        List<ApprovalObj> list = ApprovalConvert.INSTANCE.convertPos2Objs(pos);
        PageInfo<ApprovalObj> pageInfo = new PageInfo<>(list);
        return new PageVO<>(pageInfo.getTotal(), list);
    }

    @Override
    public List<ApprovalObj> queryApprovalByResourceIds(List<String> resourceIds,String bussesDataJson) {
        if (CollectionUtils.isEmpty(resourceIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ApprovalPo> wrapper = Wrappers.<ApprovalPo>lambdaQuery().eq(ApprovalPo::getBussesDataJson, bussesDataJson);
        wrapper.eq(ApprovalPo::getApprovalType, 4)
                .ne(ApprovalPo::getStatus, ApprovalStatusEnum.WITHDRAWN.getCode())
                .in(ApprovalPo::getResourceId, resourceIds);
        List<ApprovalPo> approvalPos = this.list(wrapper);
        return ApprovalConvert.INSTANCE.convertPos2Objs(approvalPos);
    }

    @Override
    public List<ApprovalObj> queryApprovalByResourceIdsAndStatus(String resourceId, String projectIdJsonStr) {
        LambdaQueryWrapper<ApprovalPo> wrapper = Wrappers.<ApprovalPo>lambdaQuery().eq(ApprovalPo::getBussesDataJson, projectIdJsonStr);
        wrapper.eq(ApprovalPo::getApprovalType, 4)
                .eq(ApprovalPo::getResourceId, resourceId);
        List<ApprovalPo> approvalPos = this.list(wrapper);
        return ApprovalConvert.INSTANCE.convertPos2Objs(approvalPos);
    }

    /* Started by AICoder, pid:s8523z397c20c521438d0af0c047301abfb12b42 */
    @Override
    public boolean existsCombination(String projectId, String productCategoryId, Integer approvalType) {
        LambdaQueryWrapper<ApprovalPo> wrapper = Wrappers.<ApprovalPo>lambdaQuery()
                .eq(ApprovalPo::getProjectId, projectId)
                .eq(ApprovalPo::getResourceId, productCategoryId)
                .eq(ApprovalPo::getApprovalType, approvalType)
//                .ne(ApprovalPo::getStatus, ApprovalStatusEnum.MKT_CANCEL.getCode())
//                .ne(ApprovalPo::getStatus, ApprovalStatusEnum.MKT_ACCEPTED.getCode())
                .in(ApprovalPo::getStatus, ApprovalStatusEnum.MKT_PENDING.getCode(),ApprovalStatusEnum.MKT_IN_PROGRESS.getCode(),ApprovalStatusEnum.MKT_APPROVED.getCode())
                .last("LIMIT 1"); // 匹配到第一条即终止扫描

        return this.getOne(wrapper) != null;
    }

    @Override
    public List<ApprovalObj> queryByApprovalIngInfo(ApprovalEntity approvalEntity) {
        LambdaQueryWrapper<ApprovalPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ApprovalPo::getProjectId, approvalEntity.getProjectId());
        queryWrapper.eq(ApprovalPo::getResourceId, approvalEntity.getResourceId());
        //流程状态包含"待处理"、"处理中"
        queryWrapper.in(ApprovalPo::getStatus, ApprovalStatusEnum.MKT_PENDING.getCode(), ApprovalStatusEnum.MKT_IN_PROGRESS.getCode());
        List<ApprovalPo> approvalPos = this.list(queryWrapper);
        return ApprovalConvert.INSTANCE.convertPos2Objs(approvalPos);
    }
    /* Ended by AICoder, pid:s8523z397c20c521438d0af0c047301abfb12b42 */

    /* Ended by AICoder, pid:6c7dfy62e2ga62f14d020ae7a0c2335c0a242860 */

    @Override
    public ApprovalPo queryByMaterialId(String materialId) {
        return approvalMapper.queryByMaterialId(materialId);
    }

    @Override
    public List<ApprovalObj> queryByApprovalLastInfo(ApprovalEntity approvalEntity) {
        LambdaQueryWrapper<ApprovalPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ApprovalPo::getProjectId, approvalEntity.getProjectId());
        queryWrapper.eq(ApprovalPo::getApprovalType, approvalEntity.getApprovalType());
        //流程状态包含"待处理"、"处理中"、"待验收"
        queryWrapper.in(ApprovalPo::getStatus, ApprovalStatusEnum.MKT_PENDING.getCode(), ApprovalStatusEnum.MKT_IN_PROGRESS.getCode(),ApprovalStatusEnum.MKT_APPROVED.getCode());
        List<ApprovalPo> approvalPos = this.list(queryWrapper);
        return ApprovalConvert.INSTANCE.convertPos2Objs(approvalPos);
    }
    @Override
    public ApprovalObj queryApprovalByLectotypeId(String lectotypeId) {
        LambdaQueryWrapper<ApprovalPo> wrapper = Wrappers.<ApprovalPo>lambdaQuery().eq(ApprovalPo::getBussesDataJson, lectotypeId);
        ApprovalPo approvalPo = this.baseMapper.selectOne(wrapper);
        return ApprovalConvert.INSTANCE.convertPo2Obj(approvalPo);
    }

    @Override
    public List<ApprovalObj> queryApprovalByLectotypeIdList(List<String> lectotypeIdList) {
        if (CollectionUtils.isEmpty(lectotypeIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ApprovalPo> wrapper = Wrappers.<ApprovalPo>lambdaQuery().in(ApprovalPo::getBussesDataJson, lectotypeIdList);
        List<ApprovalPo> approvalPos = this.list(wrapper);
        return ApprovalConvert.INSTANCE.convertPos2Objs(approvalPos);
    }

    /* Started by AICoder, pid:0105dybadcy1990149e808bb50fc1b10c0d3095b */
    @Override
    public List<ApprovalEntity> getApprovalInfoByCondition(String projectId, String productCategoryId, List<Integer> approvalTypes) {
        LambdaQueryWrapper<ApprovalPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ApprovalPo::getProjectId, projectId)
                .eq(ApprovalPo::getResourceId, productCategoryId)
                .in(ApprovalPo::getApprovalType, approvalTypes)
                // 运行状态为未完成(排除验收通过、已终止的市场投标支持任务)
                .ne(ApprovalPo::getStatus, ApprovalStatusEnum.MKT_CANCEL.getCode())
                .ne(ApprovalPo::getStatus, ApprovalStatusEnum.MKT_ACCEPTED.getCode());

        List<ApprovalPo> poList = this.baseMapper.selectList(queryWrapper);
        return ApprovalConvert.INSTANCE.approvalPoToListEntity(poList);
    }

    @Override
    public List<ApprovalEntity> queryTaskStatisticsListByApprovalTypeList(List<Integer> approvalTypeList) {
        List<ApprovalPo> approvalPos =approvalMapper.queryTaskStatisticsListByApprovalTypeList(approvalTypeList);
        if (CollectionUtils.isEmpty(approvalPos)){
            return Collections.emptyList();
        }
        return ApprovalConvert.INSTANCE.approvalPoToListEntity(approvalPos);
    }

    /* Ended by AICoder, pid:0105dybadcy1990149e808bb50fc1b10c0d3095b */
}
