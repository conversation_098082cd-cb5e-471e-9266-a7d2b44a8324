/* Started by AICoder, pid:n3387k8f67f62d5145190b31003e6c7062939889 */
package com.zte.uedm.dcdigital.domain.utils;

import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

@Slf4j
public class DateUtils {
    private static final DateTimeFormatter INPUT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter OUTPUT_FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 解析日期字符串为LocalDate对象
     *
     * @param dateStr 日期字符串（格式：yyyy-MM-dd）
     * @return 解析成功的LocalDate对象，解析失败返回null
     */
    public static LocalDate parseDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDate.parse(dateStr, DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            return null;
        }
    }

    /**
     * 判断任务是否延期
     *
     * @param actualCompleteDate 实际完成时间（yyyy-MM-dd）
     * @param dueDate            规定完成时间（yyyy-MM-dd）
     * @param currentDate        当前日期（yyyy-MM-dd）
     * @return true = 延期；false = 未延期
     */
    public static boolean isDelayed(String actualCompleteDate, String dueDate, String currentDate) {
        LocalDate actual = parseDate(actualCompleteDate);
        LocalDate due = parseDate(dueDate);
        LocalDate current = parseDate(currentDate);

        if (due == null) return false; // 没有规定时间，默认不算延期

        if (actual != null) {
            return actual.isAfter(due); // 实际完成 > 规定时间 → 延期
        } else {
            return due.isBefore(current); // 未完成，规定时间 < 当前时间 → 延期
        }
    }

    /**
     * 将完整时间格式转换为短日期格式
     *
     * @param dateTimeStr 原始时间字符串（格式：yyyy-MM-dd HH:mm:ss）
     * @return 格式化后的日期字符串（yyyy-MM-dd），解析失败返回null
     */
    public static String formatToShortDate(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }
        try {
            LocalDateTime dateTime = LocalDateTime.parse(dateTimeStr, INPUT_FORMATTER);
            return dateTime.format(OUTPUT_FORMATTER);
        } catch (DateTimeParseException e) {
            log.error("时间格式解析失败：{}", dateTimeStr);
            return null;
        }
    }
}

/* Ended by AICoder, pid:n3387k8f67f62d5145190b31003e6c7062939889 */