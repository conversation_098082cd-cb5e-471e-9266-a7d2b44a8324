package com.zte.uedm.dcdigital.domain.repository;

import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.domain.model.approval.ApprovalObj;
import com.zte.uedm.dcdigital.domain.model.approval.ApprovalWithResultObj;
import com.zte.uedm.dcdigital.domain.model.process.ApprovalEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ApprovalPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.CompletedApprovalQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.InitiatedApprovalQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.PendingApprovalQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProcessPendingDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.ApprovalVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.TaskDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ApprovalRepository {



    /* Started by AICoder, pid:v686df919973b8b14dff087a80137e0bf9d78675 */
    /**
     * 根据流程实例ID查询审批实体。
     *
     * @param flowId 流程实例ID，用于标识特定的审批流程实例。
     * @return 返回与指定流程实例ID匹配的审批实体。如果没有找到匹配项，则返回null。
     */
    ApprovalEntity queryByFlowId(String flowId);
    /* Ended by AICoder, pid:v686df919973b8b14dff087a80137e0bf9d78675 */

    /**
     * 根据流程实例ID合集查询审批实体集合。
     *
     * @param flowIds 流程实例ID合集，用于标识特定的审批流程实例。
     * @return 返回与指定流程实例ID匹配的审批实体。如果没有找到匹配项，则返回null。
     */
    List<ApprovalEntity> queryApprovalListByFlowIds(List<String> flowIds);
    /* Started by AICoder, pid:na7d1p2e41d934d14d210a38a0a19b09b7173d71 */
    /**
     * 更新审批实体的审批状态。
     *
     * @param approvalEntity 包含更新后审批状态的审批实体。
     * @return 如果更新成功，返回 `true`；否则返回 `false`。
     */
    boolean updateApprovalStatus(ApprovalEntity approvalEntity);
    /* Ended by AICoder, pid:na7d1p2e41d934d14d210a38a0a19b09b7173d71 */

    List<ApprovalObj> queryPendingList(PendingApprovalQueryDto queryDto, List<String> userIds);
    List<ApprovalObj> queryPendingListNew(ProcessPendingDto processBo );
    List<ApprovalObj> queryInitiatedList(InitiatedApprovalQueryDto queryDto, List<String> userIds);
    List<ApprovalWithResultObj> queryCompletedList(String userId, CompletedApprovalQueryDto queryDto, List<String> userIds);
    ApprovalObj getById(String id);
    List<ApprovalObj> getByFlowIds(List<String> ids);
    void updateBussesData(String flowId, String data);
    void createApproval(ApprovalEntity approvalEntity);
    void updateFlowIdById(String id, String flowId);

    ApprovalObj getByFlowId(String flowId);

    ApprovalObj selectFlowIdByCategoryIdAndProjectId(String projectId, String productCategoryId);


    /* Started by AICoder, pid:t1771hf48a0c354143df0ad880df075731149d16 */
    /**
     * 查询任务详情。
     *
     * <p>
     * 该方法根据业务数据JSON、页码和每页大小查询任务详情，并返回分页结果。
     * </p>
     *
     * @param bussesDataJson 业务数据的JSON字符串，用于过滤任务。
     * @param pageNum        页码，从1开始。
     * @param pageSize       每页显示的任务数量。
     * @return 包含任务详情的分页对象。如果没有找到任何任务，将返回一个空的分页对象。
     */
    PageVO<TaskDetailVo> queryTaskDetails(String bussesDataJson, Integer pageNum, Integer pageSize);
    PageVO<TaskDetailVo> queryTaskDetailsByProjectId(String projectId, Integer pageNum, Integer pageSize);

    /**
     * 根据资源ID查询任务实体列表。
     *
     * <p>
     * 该方法根据业务数据JSON查询与指定资源相关的所有任务实体。
     * </p>
     *
     * @param bussesDataJson 业务数据的JSON字符串，用于过滤任务。
     * @return 包含任务实体的列表。如果没有找到任何任务，将返回一个空列表。
     */
    List<ApprovalObj> queryTaskByResourceId(String bussesDataJson);

    List<ApprovalObj> queryApprovalByProjectId(String projectId);
    PageVO<ApprovalObj> queryApprovalByProjectIdAndType(String projectId, Integer type, Integer pageNum, Integer pageSize);

    /**
     * 根据资源ID列表查询审批对象列表。
     *
     * <p>
     * 该方法根据业务数据JSON和资源ID列表查询与这些资源相关的所有审批对象。
     * 如果传入的资源ID列表为空或null，将返回一个空列表。
     * </p>
     *
     * @param resourceIds    资源ID列表，用于过滤审批对象。
     * @param bussesDataJson 业务数据的JSON字符串，用于过滤审批对象。
     * @return 包含审批对象的列表。如果没有找到任何审批对象，将返回一个空列表。
     */
    List<ApprovalObj> queryApprovalByResourceIds(List<String> resourceIds, String bussesDataJson);

    /**
     * 根据资源ID和状态查询审批对象列表。
     *
     * <p>
     * 该方法根据项目JSON字符串、资源ID和状态列表查询与这些条件匹配的所有审批对象。
     * 如果传入的状态列表为空或null，将返回一个空列表。
     * </p>
     *
     * @param resourceId     资源ID，用于过滤审批对象。
     * @param projectIdJsonStr 项目数据的JSON字符串，用于过滤审批对象。
     * @return 包含审批对象的列表。如果没有找到任何审批对象，将返回一个空列表。
     */
    List<ApprovalObj> queryApprovalByResourceIdsAndStatus(String resourceId, String projectIdJsonStr);

    /**
     * 根据项目ID、产品子类别和审批类型查询是否存在组合。
     *
     * <p>
     * 该方法根据项目ID、产品子类别和审批类型查询是否存在组合。
     * 如果存在，则返回true；否则返回false。
     * </p>
     *
     * @param projectId        项目ID，用于标识特定的项目。
     * @param productCategoryId 产品类别ID，用于标识特定的产品类别。
     * @param approvalType    审批类型，用于标识特定的审批类型。
     */
    boolean existsCombination(String projectId, String productCategoryId, Integer approvalType);

    /**
     *
     * */
    List<ApprovalObj> queryByApprovalIngInfo(ApprovalEntity approvalEntity);
    /* Ended by AICoder, pid:t1771hf48a0c354143df0ad880df075731149d16 */

    ApprovalPo queryByMaterialId(String materialId);

    List<ApprovalObj> queryByApprovalLastInfo(ApprovalEntity approvalEntity);

    ApprovalObj queryApprovalByLectotypeId(String lectotypeId);

    List<ApprovalObj> queryApprovalByLectotypeIdList(List<String> lectotypeIdList);

    List<ApprovalEntity> getApprovalInfoByCondition(String projectId, String productCategoryId, List<Integer> approvalTypes);

    /**
     * 统计指定类型的任务流程数据
     * */
    List<ApprovalEntity> queryTaskStatisticsListByApprovalTypeList(List<Integer> approvalTypeList);
}
