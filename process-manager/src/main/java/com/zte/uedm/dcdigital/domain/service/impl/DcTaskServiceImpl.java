package com.zte.uedm.dcdigital.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.uedm.dcdigital.common.bean.dto.LaunchBidDto;
import com.zte.uedm.dcdigital.common.bean.process.MktApprovalCancelDto;
import com.zte.uedm.dcdigital.common.bean.process.MktApprovalInfoQueryDto;
import com.zte.uedm.dcdigital.common.bean.process.MktApprovalInfoVo;
import com.zte.uedm.dcdigital.common.bean.system.UserVo;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.common.constant.MktTaskConstants;
import com.zte.uedm.dcdigital.domain.common.constant.MktTaskNumMapConstants;
import com.zte.uedm.dcdigital.domain.common.constant.ProcessConstants;
import com.zte.uedm.dcdigital.domain.common.constant.TaskConstants;
import com.zte.uedm.dcdigital.domain.common.enums.*;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProcessStatusCode;
import com.zte.uedm.dcdigital.domain.model.process.ApprovalCommentEntity;
import com.zte.uedm.dcdigital.domain.model.process.ApprovalEntity;
import com.zte.uedm.dcdigital.domain.model.process.TaskBo;
import com.zte.uedm.dcdigital.domain.repository.MktApprovalAssocInfoRepository;
import com.zte.uedm.dcdigital.domain.service.ApprovalCommentService;
import com.zte.uedm.dcdigital.domain.service.ApprovalService;
import com.zte.uedm.dcdigital.domain.service.DcTaskService;
import com.zte.uedm.dcdigital.interfaces.web.dto.MktApprovalAssocInfoDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.MktApprovalSubmitDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.ApprovalVo;
import com.zte.uedm.dcdigital.sdk.project.service.ProjectService;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.sdk.system.service.SystemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.common.engine.api.FlowableObjectNotFoundException;
import org.flowable.common.engine.impl.identity.Authentication;
import org.flowable.engine.*;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.task.api.DelegationState;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DcTaskServiceImpl implements DcTaskService {

    // 创建ObjectMapper实例
    ObjectMapper mapper = new ObjectMapper();

    @Resource
    private TaskService taskService;

    @Resource
    private AuthService authService;

    @Resource
    private IdentityService identityService;

    @Resource
    private RepositoryService repositoryService;

    @Resource
    private ApprovalService approvalService;

    @Resource
    private ApprovalCommentService approvalCommentService;

    @Resource
    private RuntimeService runtimeService;

    @Resource
    private HistoryService historyService;

    @Resource
    private SystemService systemService;

    @Autowired
    ProcessEngine processEngine;

    @Resource
    private MktApprovalAssocInfoRepository mktApprovalAssocInfoRepository;

    @Autowired
    private ProjectService projectService;

    @Override
    public void startFistTask(ProcessInstance processInstance, Map<String, Object> variables) {
        // 若第一个用户任务为发起人，则自动完成任务
        List<Task> tasks = taskService.createTaskQuery().processInstanceId(processInstance.getId()).list();
        log.info("startFistTask, processInstanceId: {}, variables: {}", processInstance.getId(), variables);
        if (CollUtil.isNotEmpty(tasks)) {
            // 获取任务发起人
            String initiatorId = (String) variables.get(TaskConstants.PROCESS_INITIATOR);
            String userId = authService.getUserId();
            identityService.setAuthenticatedUserId(userId);
            UserVo userinfo = systemService.getUserinfoById(userId);
            log.info("userId: {}", userId);
            if (userinfo != null) {
                for (Task task : tasks) {
                    if (StrUtil.equals(task.getAssignee(), initiatorId)) {
                        // todo 后面将把发起人的名字加上
                        taskService.addComment(task.getId(), processInstance.getProcessInstanceId(), null, userinfo.getDisplayText() + "发起流程申请");
                        taskService.complete(task.getId(), variables);
                    }
                }
            } else {
                throw new BusinessException(ProcessStatusCode.USER_NOT_FOUND);
            }
        }
    }

    /* Started by AICoder, pid:i7af5e0e6bt2aff14ab90bb8b013544f1c12590d */

    /**
     * 完成任务。
     *
     * @param taskBo 包含任务操作结果的任务业务对象。
     * @return 如果任务完成成功，返回 `true`；否则返回 `false`。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean complete(TaskBo taskBo) {
        Task task = taskService.createTaskQuery().taskId(taskBo.getTaskId()).singleResult();
        if (ObjectUtil.isNull(task)) {
            log.error("task not found, taskId:{}", taskBo.getTaskId());
            throw new BusinessException(ProcessStatusCode.TASK_NOT_FOUND);
        }
        if (task.isSuspended()) {
            throw new BusinessException(ProcessStatusCode.TASK_SUSPENDED);
        }
        // 获取当前处理人
        String userId = authService.getUserId();
        identityService.setAuthenticatedUserId(userId);
        // 转派逻辑，现在用不到先写在这简单处理下
        if (DelegationState.PENDING.equals(task.getDelegationState())) {
            taskService.addComment(task.getId(), task.getProcessInstanceId(), FlowCommentEnum.DELEGATE.getType(), taskBo.getComment());
            taskService.resolveTask(taskBo.getTaskId());
            return true;
        } else {

            taskService.setAssignee(task.getId(), userId);
            Map<String, Object> variables = taskBo.getVariables();
            if (ObjectUtil.isNotEmpty(variables)) {
                variables.put(ProcessConstants.PROCESS_APPROVAL, taskBo.getResult());
            } else {
                variables = new HashMap<>();
                variables.put(ProcessConstants.PROCESS_APPROVAL, taskBo.getResult());
                taskBo.setVariables(variables);
            }
            if (taskBo.getResult().equals(TaskConstants.TASK_APPROVAL)) {
                taskService.addComment(task.getId(), task.getProcessInstanceId(), FlowCommentEnum.NORMAL.getType(), taskBo.getComment());

            } else {
                taskService.addComment(task.getId(), task.getProcessInstanceId(), FlowCommentEnum.REJECT.getType(), taskBo.getComment());

            }
            boolean nodeEvent1= isNextNodeEvent(task);
            log.info("nodeEvent:{},{}",nodeEvent1,task.getId());
            if (nodeEvent1) {
                updateProcessStatus(task);
            }
            // 完成任务
            taskService.complete(taskBo.getTaskId(), variables);


            // 根据审批结果执行不同业务逻辑
            boolean nodeEvent = isProcessInstanceEnded(taskBo.getProcInsId());


            // 判断当前操作是通过还是拒绝
            if (taskBo.getResult().equals(TaskConstants.TASK_APPROVAL)) {
                if (nodeEvent) {
                    approvalService.updateApprovalStatus(taskBo.getProcInsId(), ApprovalStatusEnum.APPROVED.getCode());
                } else {
                    approvalService.updateApprovalStatus(taskBo.getProcInsId(), ApprovalStatusEnum.IN_PROGRESS.getCode());
                }
            } else {
                approvalService.updateApprovalStatus(taskBo.getProcInsId(), ApprovalStatusEnum.REJECTED.getCode());
            }
            createAndAddComment(task, taskBo);

            return true;
        }
    }
    /* Ended by AICoder, pid:i7af5e0e6bt2aff14ab90bb8b013544f1c12590d */

    /* Started by AICoder, pid:6a9d3c9ca8a1e95140c80b47c030aa4625179cdd */

    /**
     * 停止指定流程实例。
     *
     * @param processInstanceId 流程实例ID。
     * @return 如果停止成功，返回 `true`；否则返回 `false`。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean stopProcess(String processInstanceId) {
        // 判断当前任务是否可以取消
        List<Task> taskList = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
        log.info("taskList:{}", taskList);
        if (CollectionUtils.isEmpty(taskList)) {
            throw new BusinessException(ProcessStatusCode.PROCESS_NOT_STARTED_OR_COMPLETED);
        }
        String userId = authService.getUserId();
        // 判断当前用户是否为流程发起人
        if (!isCurrentUserTheInitiator(processInstanceId, userId)) {
            throw new BusinessException(ProcessStatusCode.CURRENT_USER_IS_NOT_THE_INITIATOR);
        }
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processInstance.getProcessDefinitionId());
        if (Objects.nonNull(bpmnModel)) {
            Process process = bpmnModel.getMainProcess();
            List<EndEvent> endNodes = process.findFlowElementsOfType(EndEvent.class, false);
            if (CollectionUtils.isNotEmpty(endNodes)) {
                Authentication.setAuthenticatedUserId(userId);
                // 设置自定义流程状态
                runtimeService.setVariable(processInstanceId, ProcessConstants.PROCESS_STATUS_KEY, ProcessStatusEnum.CANCELED.getStatus());
                UserVo userinfo = systemService.getUserinfoById(userId);
                if (userinfo == null) {
                    throw new BusinessException(ProcessStatusCode.USER_NOT_FOUND);
                }
                for (Task task : taskList) {
                    taskService.addComment(task.getId(), processInstance.getProcessInstanceId(), FlowCommentEnum.STOP.getType(), userinfo.getDisplayText() + "取消流程");
                }
                // 获取当前流程最后一个节点
                String endId = endNodes.get(0).getId();
                List<Execution> executions = runtimeService.createExecutionQuery()
                        .parentId(processInstance.getProcessInstanceId()).list();
                List<String> executionIds = new ArrayList<>();
                executions.forEach(execution -> executionIds.add(execution.getId()));
                // 变更流程为已结束状态
                runtimeService.createChangeActivityStateBuilder()
                        .moveExecutionsToSingleActivityId(executionIds, endId).changeState();

                // 更新审批记录表数据
                return approvalService.updateApprovalStatus(processInstanceId, ApprovalStatusEnum.WITHDRAWN.getCode());
            }
        }
        return false;
    }
    /* Ended by AICoder, pid:6a9d3c9ca8a1e95140c80b47c030aa4625179cdd */
    @Override
    public boolean stopMProcess(String processInstanceId) {
        // 判断当前任务是否可以取消
        List<Task> taskList = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
        log.info("taskList:{}", taskList);
        if (CollectionUtils.isEmpty(taskList)) {
            throw new BusinessException(ProcessStatusCode.PROCESS_NOT_STARTED_OR_COMPLETED);
        }

        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processInstance.getProcessDefinitionId());
        if (Objects.nonNull(bpmnModel)) {
            Process process = bpmnModel.getMainProcess();
            List<EndEvent> endNodes = process.findFlowElementsOfType(EndEvent.class, false);
            if (CollectionUtils.isNotEmpty(endNodes)) {
                // 设置自定义流程状态
                runtimeService.setVariable(processInstanceId, ProcessConstants.PROCESS_STATUS_KEY, ProcessStatusEnum.CANCELED.getStatus());

                for (Task task : taskList) {
                    taskService.addComment(task.getId(), processInstance.getProcessInstanceId(), FlowCommentEnum.STOP.getType(), "删除物料自动取消流程");
                }
                // 获取当前流程最后一个节点
                String endId = endNodes.get(0).getId();
                List<Execution> executions = runtimeService.createExecutionQuery()
                        .parentId(processInstance.getProcessInstanceId()).list();
                List<String> executionIds = new ArrayList<>();
                executions.forEach(execution -> executionIds.add(execution.getId()));
                // 变更流程为已结束状态
                runtimeService.createChangeActivityStateBuilder()
                        .moveExecutionsToSingleActivityId(executionIds, endId).changeState();

                // 更新审批记录表数据
                return approvalService.updateApprovalStatus(processInstanceId, ApprovalStatusEnum.WITHDRAWN.getCode());
            }
        }
        return false;
    }


    /* Started by AICoder, pid:t89c55a0d89251714e0c0b38b07fca0aa005e85e */
    @Override
    public List<Task> getTasksByProcessInstanceIdAndTaskId(String procInsId, String taskId) {
        String userId = authService.getUserId();
        return taskService.createTaskQuery().processInstanceId(procInsId).taskId(taskId).taskAssignee(userId).list();
    }
    /* Ended by AICoder, pid:t89c55a0d89251714e0c0b38b07fca0aa005e85e */

    @Override
    public Task getTaskKey(String procInsId, String taskId) {
        return taskService.createTaskQuery().processInstanceId(procInsId).taskId(taskId).singleResult();
    }

    @Override
    public void claimTaskCandidate(String taskId) {
        // 查询任务详情
        Task task = processEngine.getTaskService().createTaskQuery().taskId(taskId).singleResult();
        if (task == null) {
            throw new BusinessException(ProcessStatusCode.TASK_NOT_FOUND);
        }
        String userId = authService.getUserId();
        // 查询当前候选任务的候选用户
        List<IdentityLink> identityLinks = processEngine.getTaskService().getIdentityLinksForTask(taskId);
        List<String> ids = identityLinks.stream().map(IdentityLink::getUserId).collect(Collectors.toList());
        if (!ids.contains(userId)) {
            //当前用户不是候选用户
            log.error("Current user is not a candidate user,taskId:{},userId:{}", taskId, userId);
            throw new BusinessException(ProcessStatusCode.TASK_NOT_FOUND);
        }
        //认领任务
        taskService.claim(taskId, userId);
    }

    //转交任务(已弃用)
    @Override
    @Transactional
    public void setMktAssignee(MktApprovalSubmitDto dto,TaskBo taskBo) {
        //当前登录用户
        String currentUserId = authService.getUserId();
        //目标转交用户
        String targetUserId = dto.getTransId();
        Task task = processEngine.getTaskService().createTaskQuery().taskId(dto.getTaskId()).singleResult();
        if (task == null) {
            throw new BusinessException(ProcessStatusCode.TASK_NOT_FOUND);
        }
        // 从当前任务获取流程实例ID
        String processInstanceId = task.getProcessInstanceId();
        //获取流程变量产品小类id
        String productCategoryId = (String) runtimeService.getVariable(processInstanceId, ProcessConstants.PRODUCT_CATEGORY);
        //我已处理"转交"状态
        //taskService.addComment(task.getId(), task.getProcessInstanceId(), MktAcceptTypeEnum.TRANSFER.getCode(), taskBo.getComment());
        taskBo.setResult(Integer.valueOf(MktAcceptTypeEnum.TRANSFER.getCode()));
        createAndAddComment(task,taskBo);
        String projectId = (String) runtimeService.getVariable(processInstanceId, TaskConstants.PROJECT_ID);
        //判断当前要转交的任务是否是补充主任务节点(带有子任务的主任务需要同时转交子任务)
        if (MktTaskConstants.REP_MAIN_TASK_ID.equals(task.getTaskDefinitionKey())) {
            //确认为补充主任务转交，需要转交补充主任务以及转交未完成的子任务
            // 查询流程实例下所有未完成任务
            List<Task> subTasks = taskService.createTaskQuery()
                    .processInstanceId(processInstanceId)
                    .active() // 仅查询未完成任务
                    .list();
            // 筛选目标节点任务
            List<Task> targetTasks = subTasks.stream()
                    .filter(t -> MktTaskConstants.TARGET_TASK_KEYS.contains(t.getTaskDefinitionKey()))
                    .collect(Collectors.toList());
            //批量修改目标节点的任务处理人
            targetTasks.forEach(t -> {
                processEngine.getTaskService().setAssignee(t.getId(), targetUserId);
                addProjectPermission(targetUserId,dto.getTransName(), dto.getProjectId(),productCategoryId);
            });
            //判断当前项目、指定的产品小类是否已保存"市场投标支持se"，存在则更新;
            MktApprovalInfoQueryDto queryDto = new MktApprovalInfoQueryDto();
            queryDto.setProjectId(projectId);
            queryDto.setProductCategoryId(productCategoryId);
            queryDto.setTaskHandlerId(currentUserId);
            //获取当前任务的TaskKey
            queryDto.setTaskId(task.getTaskDefinitionKey());
            List<MktApprovalInfoVo> voList = mktApprovalAssocInfoRepository.selectMarketBidInfo(queryDto);
            if (CollectionUtils.isNotEmpty(voList)) {
                //存在则更新未完成任务记录的处理人
                for (MktApprovalInfoVo vo : voList){
                    MktApprovalAssocInfoDto updateDto = new MktApprovalAssocInfoDto();
                    //id
                    updateDto.setId(vo.getId());
                    //市场产品se
                    updateDto.setProductSeId(targetUserId);
                    //当前任务处理人
                    updateDto.setTaskHandlerId(targetUserId);
                    mktApprovalAssocInfoRepository.updateMarketBidInfo(updateDto);
                }
            }
            return;
        }
        //任务转交
        processEngine.getTaskService().setAssignee(dto.getTaskId(), targetUserId);
        //添加市场投标支持与处理人的关系
        addMarketBidInfo(projectId,processInstanceId,productCategoryId,"",targetUserId,targetUserId, task.getTaskDefinitionKey());
        //为转接用户赋予项目查看权限
        addProjectPermission(targetUserId,dto.getTransName(), dto.getProjectId(),productCategoryId);
    }

    @Override
    @Transactional
    public void cancelMarketBidApproval(MktApprovalCancelDto cancelDto) {
//        String userId = authService.getUserId();
        for (String productCategoryId : cancelDto.getProductCategoryId()) {
            //获取需要取消的flowId
            String flowId=cancelDto.getFlowId();
            // 判断当前用户是否为流程发起人
//            if (!isCurrentUserTheInitiator(flowId, userId)) {
//                throw new BusinessException(ProcessStatusCode.CURRENT_USER_IS_NOT_THE_INITIATOR);
//            }
            //判断流程是否已经完成
            if (isProcessCompleted(flowId)) {
                //流程已结束，放行
                log.info("流程已结束，放行");
                return;
            }
            //更新每种类型的子任务存储流程变量的任务个数
            updateMaterielTaskCount(flowId,cancelDto.getProjectId());
            updateTenderTaskCount(flowId,cancelDto.getProjectId());
            updateDocumentTaskCount(flowId,cancelDto.getProjectId());
            // 更新审批记录表数据
            //市场投标支持 流程被取消目前用的也是之前物料的状态码  "已验收"
            approvalService.updateApprovalStatus(flowId, ApprovalStatusEnum.MKT_ACCEPTED.getCode());
            //添加处理记录
            interruptMkTask(flowId);
            //终止流程实例及其所有任务
            terminateProcessInstance(flowId, "system");
        }

    }

    /**
     * 终止流程实例及其所有任务
     * @param processInstanceId 流程实例ID
     * @param reason 终止原因
     * @return 操作结果
     */
    public String terminateProcessInstance(String processInstanceId, String reason) {
        // 1. 校验流程实例是否存在且未结束
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();

        if (processInstance == null) {
            return "流程实例不存在或已结束";
        }

        try {
            // 2. 终止流程实例（自动清理所有任务和执行实例）
            runtimeService.deleteProcessInstance(processInstanceId, reason);
            return "流程终止成功";
        } catch (Exception e) {
            return "流程终止失败: " + e.getMessage();
        }
    }




    //查询当前流程未完成的任务
    public List<Task> findUnclaimedTasksByProcessInstanceId(String processInstanceId) {
        return processEngine.getTaskService()
                .createTaskQuery()
                .processInstanceId(processInstanceId)
                .list();
    }
    //抽取的方法判断当前流程是否还在运行
    public boolean isProcessCompleted(String processInstanceId) {
        // 查询流程实例
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();
        // 如果查询不到，说明流程已结束
        return processInstance == null;
    }

    //取消任务具体方法逻辑
    private void interruptMkTask(String processInstanceId) {
        //当前流程下是否产生候选子任务，若未产生则只需要完成主任务，
        List<Task> tasks= findUnclaimedTasksByProcessInstanceId(processInstanceId);
        if (CollectionUtils.isEmpty(tasks)) {
            log.error("Subtask not found, completing the main task");
            throw new BusinessException(ProcessStatusCode.PROCESS_NOT_STARTED_OR_COMPLETED);
        }
        if (tasks.size() == 1) {
            log.info("Only one task, completing the main task");
            //只有一个任务(主任务)，直接完成主任务
            Task task = tasks.get(0);
            String schemeSeId = (String) runtimeService.getVariable(processInstanceId, TaskConstants.PROCESS_INITIATOR);
            setStopSubMktTaskInfo(processInstanceId, task, task.getTaskDefinitionKey(), task.getName(), schemeSeId);
            return;
        }
        //超过1个任务说明包含子任务则需要将子任务也完成
        //从产品小类路径名称从nodeName中取值
        for (String staticTaskId:MktTaskConstants.SUB_TASK_IDS){
            // 获取当前任务实例
            Task task = taskService.createTaskQuery()
                    .processInstanceId(processInstanceId)
                    .taskDefinitionKey(staticTaskId) // 物料选型任务ID
                    .singleResult();
            if (task != null) {
                String prefixName = task.getName().replaceAll("\\s+.*", "");
                // 设置审批结果变量
                runtimeService.setVariable(task.getExecutionId(), ProcessConstants.PROCESS_APPROVAL, TaskConstants.TASK_APPROVAL);
                log.info("Task has been forced to complete and jumped to the verification passed branch");
                String schemeSeId = (String) runtimeService.getVariable(processInstanceId, TaskConstants.PROCESS_INITIATOR);
                //setStopSubMktTaskInfo(processInstanceId, task, staticTaskId,prefixName,schemeSeId);
            }

        }


    }

    private void setStopSubMktTaskInfo(String processInstanceId, Task task, String taskKey, String prefixName, String schemeSeId) {
        TaskBo taskBo = new TaskBo();
        ApprovalVo approvalVo = approvalService.getApprovalByFlowId(processInstanceId);
        if (approvalVo==null){
            return;
        }
        //审批单id
        taskBo.setProcessBusinessKey(approvalVo.getId());
        //完成状态
        taskBo.setResult(Integer.valueOf(MktAcceptTypeEnum.COMPLETE.getCode()));
        //备注为system处理
        taskBo.setComment(TaskConstants.PROCESS_EXCEPTION_HANDLER);
        String suffixName = "";
        if (MktTaskNumMapConstants.TASK_SUFFIX_MAP.get(taskKey) != null) {
            suffixName = MktTaskNumMapConstants.TASK_SUFFIX_MAP.get(taskKey);
        }
        task.setName(prefixName + " " + suffixName);
        createMktSubTaskAddComment(task, taskBo, taskKey, schemeSeId);
    }


    //更新物料选型子任务个数
    private void updateMaterielTaskCount(String flowId,String projectId) {
        int materielTaskCount;
        Object materielValue = runtimeService.getVariable(flowId, MktTaskConstants.MATERIEL_TASK_NUM);
        if (materielValue instanceof Integer) {
            materielTaskCount = (Integer) materielValue;
        } else {
            // 处理脏数据的情况，设置默认值
            materielTaskCount = 0;
        }
        //设置取消后的taskCount变量(删除一个工程量清单对应流程减少一个任务)
        materielTaskCount--;
        runtimeService.setVariable(flowId,MktTaskConstants.MATERIEL_TASK_NUM, materielTaskCount);
        //如果是当前流程的最后一个物料选型子任务
        updateProjectDataCondition(materielTaskCount,MktTaskConstants.MATERIEL_TASK_NUM, flowId, projectId);
    }
    //更新标书澄清子任务个数
    private void updateTenderTaskCount(String flowId,String projectId) {
        int tenderTaskCount;
        Object tenderValue =runtimeService.getVariable(flowId, MktTaskConstants.TENDER_TASK_NUM);
        if (tenderValue instanceof Integer) {
            tenderTaskCount = (Integer) tenderValue;
        } else {
            // 处理脏数据的情况，设置默认值
            tenderTaskCount = 0;
        }
        //设置取消后的taskCount变量(删除一个工程量清单对应流程减少一个任务)
        tenderTaskCount--;
        runtimeService.setVariable(flowId,MktTaskConstants.TENDER_TASK_NUM, tenderTaskCount);
        //如果是当前流程的最后一个物料选型子任务
        updateProjectDataCondition(tenderTaskCount,MktTaskConstants.TENDER_TASK_NUM, flowId, projectId);
    }
    //更新文件定稿子任务个数
    private void updateDocumentTaskCount(String flowId,String projectId) {
        int documentTaskCount;
        Object documentValue =runtimeService.getVariable(flowId, MktTaskConstants.DOCUMENT_TASK_NUM);
        if (documentValue instanceof Integer) {
            documentTaskCount = (Integer) documentValue;
        } else {
            // 处理脏数据的情况，设置默认值
            documentTaskCount = 0;
        }
        //设置取消后的taskCount变量(删除一个工程量清单对应流程减少一个任务)
        documentTaskCount--;
        runtimeService.setVariable(flowId,MktTaskConstants.DOCUMENT_TASK_NUM, documentTaskCount);
        //如果是当前流程的最后一个物料选型子任务
        updateProjectDataCondition(documentTaskCount,MktTaskConstants.DOCUMENT_TASK_NUM, flowId, projectId);
    }

    //更新项目对应阶段的锁定时间
    private void updateProjectDataCondition(int taskCount, String taskNumKey, String currentActivityId, String projectId) {
        //判断对应子任务值是否等于0,等于0则更新项目阶段时间(小于0的条件是因为这个需求是在上线后提出的，为了避免脏数据)
        LaunchBidDto launchBidDto = new LaunchBidDto();
        //不知道为什么传递的projectId是JSON格式
        launchBidDto.setId(projectId);
        if (taskCount<=0&&taskNumKey.equals(MktTaskConstants.MATERIEL_TASK_NUM)){
            //更新配置清单锁定时间
            updateConfigureManifestLockTime(launchBidDto);
        }else if (taskCount<=0&&taskNumKey.equals(MktTaskConstants.TENDER_TASK_NUM)){
            //更新澄清提交时间
            updateClarifySubmissionTime(launchBidDto);
        } else if (taskCount<=0&&taskNumKey.equals(MktTaskConstants.DOCUMENT_TASK_NUM)){
            //更新招标文件定稿时间
            updateBiddingDocumentsFinalizationTime(launchBidDto);
        } else if (taskCount<=0&&taskNumKey.equals(MktTaskNumMapConstants.DEFAULT_TASK_NUM)){
            log.warn("Task count not updated for activity: {}", currentActivityId);
            //解决脏数据问题更新所有时间
            String currentTime = DateTimeUtils.getCurrentTime();
            //澄清提交时间
            launchBidDto.setClarifySubmissionTime(currentTime);
            //配置清单锁定时间
            launchBidDto.setConfigureManifestLockTime(currentTime);
            //招标文件定稿时间
            launchBidDto.setBiddingDocumentsFinalizationTime(currentTime);
            //projectService.updateLaunchBidByProjectId(launchBidDto);
        }
    }

    //更新配置清单锁定时间
    private void updateConfigureManifestLockTime(LaunchBidDto launchBidDto) {
        String currentTime = DateTimeUtils.getCurrentDate();
        //配置清单锁定时间
        log.info("update ConfigureManifestLockTime: {}", currentTime);
        launchBidDto.setConfigureManifestLockTime(currentTime);
        //projectService.updateLaunchBidByProjectId(launchBidDto);
    }
    //更新澄清提交时间
    private void updateClarifySubmissionTime(LaunchBidDto launchBidDto) {
        String currentTime = DateTimeUtils.getCurrentDate();
        //澄清提交时间
        log.info("update ClarifySubmissionTime: {}", currentTime);
        launchBidDto.setClarifySubmissionTime(currentTime);
        //projectService.updateLaunchBidByProjectId(launchBidDto);
    }
    //更新招标文件定稿时间
    private void updateBiddingDocumentsFinalizationTime(LaunchBidDto launchBidDto) {
        String currentTime = DateTimeUtils.getCurrentDate();
        //招标文件定稿时间
        log.info("update BiddingDocumentsFinalizationTime: {}", currentTime);
        launchBidDto.setBiddingDocumentsFinalizationTime(currentTime);
        //projectService.updateLaunchBidByProjectId(launchBidDto);
    }
    @Override
    @Transactional
    public void handleMktAccept(MktApprovalSubmitDto dto,TaskBo taskBo) {
        //当前登录用户
        String userId = authService.getUserId();
        //当前任务id
        String taskId = dto.getTaskId();
        // 通过 TaskService 查询任务实例
        TaskService taskService = processEngine.getTaskService();
        Task task = taskService.createTaskQuery()
                .taskId(taskId)
                .singleResult();
        // 校验任务是否存在
        if (task == null) {
            throw new BusinessException(ProcessStatusCode.TASK_NOT_FOUND);
        }
        // 获取当前任务对应的静态 Activity ID（即流程定义中的 userTask ID）
        RuntimeService runtimeService = processEngine.getRuntimeService();
        String currentStaticTaskId =getTaskStaticId(taskId);
        // 判断静态 ID 是否匹配补充主任务(不再拾取候选任务)
        if (MktTaskConstants.REP_MAIN_TASK_ID.equals(currentStaticTaskId)) {
            // 执行匹配后的逻辑 返回
            return;
        }
        //我已处理"接受"状态
        //taskService.addComment(taskId, task.getProcessInstanceId(), MktAcceptTypeEnum.ACCEPT.getCode(), taskBo.getComment());
        //拾取候选任务
        claimTaskCandidate(taskId);
        //获取流程实例ID
        String processInstanceId = task.getProcessInstanceId();
        //获取流程变量产品小类id
        String productCategoryId = (String) runtimeService.getVariable(processInstanceId, ProcessConstants.PRODUCT_CATEGORY);
        //获取流程变量项目id
        String projectId = (String) runtimeService.getVariable(processInstanceId, TaskConstants.PROJECT_ID);
        //取出流程中的任务发起人也就是方案se
        String schemeSeId = (String) runtimeService.getVariable(dto.getFlowId(), TaskConstants.PROCESS_INITIATOR);
        //为当前用户赋予项目查看权限
        addProjectPermission(userId,dto.getTransName(), projectId,productCategoryId);
        //拾取后立即执行完成任务(任务完成后流程会自动创建3个子任务)
        Map<String, Object> variables = new HashMap<>();
        //设置任务处理人
        variables.put(TaskConstants.PROCESS_CANDIDATE, userId);
        //书澄清处理人
        variables.put(MktTaskConstants.TENDER_CANDIDATE, userId);
        //文档验证处理人
        variables.put(MktTaskConstants.DOCUMENT_CANDIDATE, userId);
        //物料选型处理人
        variables.put(MktTaskConstants.MATERIEL_CANDIDATE, userId);
        //完成任务
        processEngine.getTaskService().complete(taskId, variables);
        taskBo.setResult(Integer.valueOf(MktAcceptTypeEnum.ACCEPT.getCode()));
        //主任务处理记录
        createAndAddComment(task,taskBo);
        //增加候选子任务的处理人(初始化时默认当前登录用户为主任务的接收人)以及子任务处理记录
        setSubMktTaskInfo(projectId, processInstanceId, productCategoryId, schemeSeId, userId,task,taskBo);
    }
    private void setSubMktTaskInfo(String projectId,String processInstanceId,String productCategoryId,String schemeSeId,String userId,Task task,TaskBo taskBo){
        //从产品小类路径名称从nodeName中取值
        String prefixName = task.getName().replaceAll("\\s+.*", "");
        for (String taskKey:MktTaskConstants.SUB_TASK_IDS){
            addMarketBidInfo(projectId, processInstanceId, productCategoryId, schemeSeId, userId, userId,taskKey);
            if (!MktTaskNumMapConstants.TASK_SUFFIX_MAP.containsKey(taskKey)) {
                log.error("Task key not found in the task_suffix_map: {}", taskKey);
                throw new BusinessException(ProcessStatusCode.PROCESS_DEFINITION_NOT_FOUND);
            }
            task.setName(prefixName+" "+MktTaskNumMapConstants.TASK_SUFFIX_MAP.get(taskKey));
            createMktSubTaskAddComment(task,taskBo,taskKey,userId);
        }
    }
    @Override
    @Transactional
    public void handleMktComplete(MktApprovalSubmitDto dto,TaskBo taskBo) {
        String taskId = dto.getTaskId();
        // 通过 TaskService 查询任务实例
        TaskService taskService = processEngine.getTaskService();
        Task task = taskService.createTaskQuery()
                .taskId(taskId)
                .singleResult();
        // 校验任务是否存在
        if (task == null) {
            throw new BusinessException(ProcessStatusCode.TASK_NOT_FOUND);
        }
        //当前登录用户
        String userId = authService.getUserId();
        //我已处理"完成"状态
        //taskService.addComment(taskId, task.getProcessInstanceId(), MktAcceptTypeEnum.COMPLETE.getCode(), taskBo.getComment());
        if (isCurrentUserTheAssigner(taskId,userId)) {
            processEngine.getTaskService().complete(taskId);
            // 从当前任务获取流程实例ID
            String processInstanceId = task.getProcessInstanceId();
            //将当前处理人记录到流程变量（使用任务ID作为key的一部分避免覆盖）
            String variableKey = "actualAssignee_" + task.getTaskDefinitionKey();
            runtimeService.setVariable(processInstanceId, variableKey, userId);
        }else {
            //当前用户不是该任务的处理人
            log.error("The current user is not the assigner of the task, taskId:{}, userId:{}", taskId, userId);
            throw new BusinessException(ProcessStatusCode.PROCESS_INSTANCE_EXIST);
        }
        taskBo.setResult(Integer.valueOf(MktAcceptTypeEnum.COMPLETE.getCode()));
        createAndAddComment(task,taskBo);
    }

    @Override
    @Transactional
    public void handleMktAcceptance(MktApprovalSubmitDto dto,TaskBo taskBo) {
        String taskId = dto.getTaskId();
        //当前登录用户
        String userId = authService.getUserId();
        // 通过 TaskService 查询任务实例
        TaskService taskService = processEngine.getTaskService();
        Task task = taskService.createTaskQuery()
                .taskId(taskId)
                .singleResult();
        // 校验任务是否存在
        if (task == null) {
            throw new BusinessException(ProcessStatusCode.TASK_NOT_FOUND);
        }
        if (isCurrentUserTheAssigner(taskId,userId)) {
            Map<String, Object> variables = new HashMap<>();
            variables.put(ProcessConstants.PROCESS_APPROVAL, TaskConstants.TASK_APPROVAL);
            //我已处理"验收通过"状态
            //taskService.addComment(taskId, task.getProcessInstanceId(), MktAcceptTypeEnum.ACCEPTANCE.getCode(),taskBo.getComment());
            //完成任务(批准)
            processEngine.getTaskService().complete(taskId, variables);
        }else {
            log.error("The current user is not the assigner of the task, taskId:{}, userId:{}", taskId, userId);
            throw new BusinessException(ProcessStatusCode.PROCESS_INSTANCE_EXIST);
        }
        taskBo.setResult(Integer.valueOf(MktAcceptTypeEnum.ACCEPTANCE.getCode()));
        createAndAddComment(task,taskBo);
    }

    @Override
    @Transactional
    public void handleMktReject(MktApprovalSubmitDto dto,TaskBo taskBo) {
        String taskId = dto.getTaskId();
        // 通过 TaskService 查询任务实例
        TaskService taskService = processEngine.getTaskService();
        Task task = taskService.createTaskQuery()
                .taskId(taskId)
                .singleResult();
        // 校验任务是否存在
        if (task == null) {
            throw new BusinessException(ProcessStatusCode.TASK_NOT_FOUND);
        }
        //当前登录用户
        String userId = authService.getUserId();
        if (isCurrentUserTheAssigner(taskId,userId)) {
            Map<String, Object> variables = new HashMap<>();
            variables.put(ProcessConstants.PROCESS_APPROVAL, TaskConstants.TASK_REJECT);
            //我已处理"验收不通过"状态
            //taskService.addComment(taskId, task.getProcessInstanceId(), MktAcceptTypeEnum.REJECT.getCode(), taskBo.getComment());
            //完成任务(拒绝)
            processEngine.getTaskService().complete(taskId, variables);
        }else {
            log.error("The current user is not the assigner of the task, taskId:{}, userId:{}", taskId, userId);
            throw new BusinessException(ProcessStatusCode.PROCESS_INSTANCE_EXIST);
        }
        taskBo.setResult(Integer.valueOf(MktAcceptTypeEnum.REJECT.getCode()));
        createAndAddComment(task,taskBo);
    }

    @Override
    public String getTaskStaticId(String taskId) {
        // 通过 TaskService 查询任务实例
        TaskService taskService = processEngine.getTaskService();
        Task task = taskService.createTaskQuery()
                .taskId(taskId)
                .singleResult();
        // 校验任务是否存在
        if (task == null) {
            log.error("Task does not exist, taskId:{}", taskId);
            throw new BusinessException(ProcessStatusCode.TASK_NOT_FOUND);
        }
        // 获取当前任务对应的静态 Activity ID（即流程定义中的 userTask ID）
        RuntimeService runtimeService = processEngine.getRuntimeService();
        Execution execution = runtimeService.createExecutionQuery()
                .executionId(task.getExecutionId())
                .singleResult();
        String currentStaticTaskId = execution.getActivityId();
        return currentStaticTaskId;
    }



    /* Started by AICoder, pid:c989dt2161d8fcc143560a8ac082ff1e73d70371 */

    /**
     * 检查当前用户是否是指定的任务处理人。
     */
    private boolean isCurrentUserTheAssigner(String taskId, String userId) {
        Task task = processEngine.getTaskService().createTaskQuery().taskId(taskId).singleResult();
        if (task == null) {
            return false;
        }
        if (!userId.equals(task.getAssignee())) {
            return false;
        }
        return true;
    }

    /**
     * 检查当前用户是否是流程的发起者。
     *
     * @param processInstanceId 流程实例ID。
     * @param userId            当前用户的ID。
     * @return 如果当前用户是流程的发起者，返回 `true`；否则返回 `false`。
     */
    private boolean isCurrentUserTheInitiator(String processInstanceId, String userId) {
        HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();
        if (processInstance != null) {
            String initiatorId = processInstance.getStartUserId();
            return userId != null && userId.equals(initiatorId);
        }
        return false;
    }
    /* Ended by AICoder, pid:c989dt2161d8fcc143560a8ac082ff1e73d70371 */

    /* Started by AICoder, pid:ee95b4d00exf1f21417d085dc07c153028214a05 */
    public boolean isProcessInstanceEnded(String processInstanceId) {
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();

        if (historicProcessInstance != null && historicProcessInstance.getEndTime() != null) {
            return true; // 流程实例已经结束
        } else {
            return false; // 流程实例未结束或不存在
        }
    }
    /**
     * 处理审批任务。
     *
     * @param task   当前任务对象。
     * @param taskBo 包含任务操作结果的任务业务对象。
     */
    private void handleApproval(Task task, TaskBo taskBo) {
        boolean nodeEvent = isProcessInstanceEnded(taskBo.getProcInsId());
        log.info("nodeEvent:{},{}",nodeEvent,task.getId());
        // 判断当前操作是通过还是拒绝
        if (taskBo.getResult().equals(TaskConstants.TASK_APPROVAL)) {
            taskService.addComment(task.getId(), task.getProcessInstanceId(), FlowCommentEnum.NORMAL.getType(), taskBo.getComment());
            if (nodeEvent) {
                approvalService.updateApprovalStatus(taskBo.getProcInsId(), ApprovalStatusEnum.APPROVED.getCode());
                updateProcessStatus(task);
            } else {
                approvalService.updateApprovalStatus(taskBo.getProcInsId(), ApprovalStatusEnum.IN_PROGRESS.getCode());
            }
        } else {
            taskService.addComment(task.getId(), task.getProcessInstanceId(), FlowCommentEnum.REJECT.getType(), taskBo.getComment());
            approvalService.updateApprovalStatus(taskBo.getProcInsId(), ApprovalStatusEnum.REJECTED.getCode());
            if (nodeEvent) {
                updateProcessStatus(task);
            }
        }
        createAndAddComment(task, taskBo);
    }
    /* Ended by AICoder, pid:ee95b4d00exf1f21417d085dc07c153028214a05 */

    /* Started by AICoder, pid:a7a1363a640d7f914c500a442050262883571800 */

    /**
     * 检查当前任务的下一个节点是否为结束事件。
     *
     * @param task 当前任务对象。
     * @return 如果下一个节点是结束事件，返回 `true`；否则返回 `false`。
     */
    private boolean isNextNodeEvent(Task task) {
        // 获取当前流程模型
        BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
        if (bpmnModel == null) {
            throw new BusinessException(ProcessStatusCode.PROCESS_MODEL_NOT_FOUND);
        }
        // 获取当前任务对应的FlowElement
        FlowElement flowElement = bpmnModel.getFlowElement(task.getTaskDefinitionKey());
        if (!(flowElement instanceof UserTask)) {
            return false;
        }

        return checkIfPathLeadsToEndEvent(flowElement, bpmnModel);
    }
    /* Ended by AICoder, pid:a7a1363a640d7f914c500a442050262883571800 */

    /* Started by AICoder, pid:fbddenf4ccub5af14f3a0ac00065d94637420561 */
    private boolean checkIfPathLeadsToEndEvent(FlowElement currentElement, BpmnModel bpmnModel) {
        // 如果当前节点是 EndEvent，返回 true
        if (currentElement instanceof EndEvent) {
            return true;
        }

        // 如果当前节点是 UserTask、ServiceTask 等任务类型，获取其流出顺序流
        if (currentElement instanceof FlowNode) {
            FlowNode flowNode = (FlowNode) currentElement;
            List<SequenceFlow> outgoingFlows = flowNode.getOutgoingFlows();

            // 如果没有流出顺序流，返回 false
            if (outgoingFlows.isEmpty()) {
                return false;
            }

            // 递归检查每个顺序流的目标节点
            for (SequenceFlow sequenceFlow : outgoingFlows) {
                FlowElement targetElement = bpmnModel.getFlowElement(sequenceFlow.getTargetRef());

                // 如果目标节点是 EndEvent，返回 true
                if (targetElement instanceof EndEvent) {
                    return true;
                }

                // 如果目标节点是 UserTask，则返回 false
                if (targetElement instanceof UserTask) {
                    return false;
                }

                // 如果目标节点是网关或任务类型，继续递归检查
                if (targetElement instanceof FlowNode) {
                    if (checkIfPathLeadsToEndEvent(targetElement, bpmnModel)) {
                        return true;
                    }
                }
            }
        }

        // 如果所有路径都不指向 EndEvent，返回 false
        return false;
    }
    /* Ended by AICoder, pid:fbddenf4ccub5af14f3a0ac00065d94637420561 */

    /* Started by AICoder, pid:g39e8aac6ai2f9b140a50a404048d11c5ff7a121 */

    /**
     * 创建并添加审批评论。
     *
     * @param task   当前任务对象。
     * @param taskBo 包含任务操作结果的任务业务对象。
     */
    private void createAndAddComment(Task task, TaskBo taskBo) {
        ApprovalCommentEntity commentEntity = new ApprovalCommentEntity();
        //审批单id
        commentEntity.setApprovalId(taskBo.getProcessBusinessKey());
        commentEntity.setComment(taskBo.getComment());
        commentEntity.setResult(taskBo.getResult());
        commentEntity.setNodeId(task.getId());
        commentEntity.setNodeName(task.getName());
        commentEntity.setFlowId(task.getProcessInstanceId());
        commentEntity.setStaticTaskId(task.getTaskDefinitionKey());
        String userId = authService.getUserId();
        commentEntity.setUserId(userId);
        approvalCommentService.addComment(commentEntity);
    }

    /**
     * 用于"市场投标支持"在接受主任务时存储子任务的处理记录
     * */
    private void createMktSubTaskAddComment(Task task, TaskBo taskBo,String staticTaskId,String userId) {
        ApprovalCommentEntity commentEntity = new ApprovalCommentEntity();
        commentEntity.setApprovalId(taskBo.getProcessBusinessKey());
        commentEntity.setComment(taskBo.getComment());
        commentEntity.setResult(taskBo.getResult());
        commentEntity.setNodeId(task.getId());
        commentEntity.setNodeName(task.getName());
        commentEntity.setFlowId(task.getProcessInstanceId());
        commentEntity.setStaticTaskId(staticTaskId);
        //方案se
        commentEntity.setUserId(userId);
        approvalCommentService.addComment(commentEntity);
    }
    /* Ended by AICoder, pid:g39e8aac6ai2f9b140a50a404048d11c5ff7a121 */

    /* Started by AICoder, pid:rcb4ck8c40qfc8e14ba00ad5b01076136592a0f0 */

    /**
     * 更新自定义流程状态，如果当前状态为 RUNNING，则将其更新为 COMPLETED。
     *
     * @param task 当前任务对象。
     */
    private void updateProcessStatus(Task task) {
        Object variable = runtimeService.getVariable(task.getProcessInstanceId(), ProcessConstants.PROCESS_STATUS_KEY);
        ProcessStatusEnum status = ProcessStatusEnum.getProcessStatus(Convert.toStr(variable));
        if (ObjectUtil.isNotNull(status) && ProcessStatusEnum.RUNNING == status) {
            runtimeService.setVariable(task.getProcessInstanceId(), ProcessConstants.PROCESS_STATUS_KEY, ProcessStatusEnum.COMPLETED.getStatus());
        }
    }

    /**
     * @param projectId 项目id
     * @param flowId 流程id
     * @param productCategoryId 产品小类id
     * @param schemeSeId 方案seId
     * @param productSeId 市场产品seId
     * @param taskHandlerId 当前处理人
     * 新增市场投标流程当前的产品SE
     * */
    public void addMarketBidInfo(String projectId, String flowId, String productCategoryId, String schemeSeId, String productSeId, String taskHandlerId,String taskKey) {
        //新增
        MktApprovalAssocInfoDto mtkDto = new MktApprovalAssocInfoDto();
        mtkDto.setId(UUID.randomUUID().toString());
        mtkDto.setProjectId(projectId);
        mtkDto.setProductCategoryId(productCategoryId);
        mtkDto.setFlowId(flowId);
        //产品小类se
        mtkDto.setProductSeId(productSeId);
        //方案se
        mtkDto.setSchemeSeId(schemeSeId);
        //任务流程节点(任务静态Id)
        mtkDto.setTaskId(taskKey);
        //任务处理人--当前登录用户
        mtkDto.setTaskHandlerId(taskHandlerId);
        mtkDto.setCreateTime(DateTimeUtils.getCurrentTime());
        mktApprovalAssocInfoRepository.addMarketBidInfo(mtkDto);
    }

    //根据用户ID和资源的ID为市场产品se赋予项目查看权限
    /**
     * @param transUserId 用户ID
     * @param transUserName 用户姓名
     * @param projectId 商机ID对应auth_resource表的entity_d 用于查询auth_resource表主键主键id
     * @param productCategoryId 产品小类id对应auth_resource表的entity_d用于查询auth_resource表主键id
     * */
    public void addProjectPermission(String transUserId,String transUserName,String projectId,String productCategoryId) {
        List<String> entityIds=new ArrayList<>();
        entityIds.add(projectId);
        entityIds.add(productCategoryId);
        log.info("Add project view permission for mkt product se,:{}",entityIds);
        systemService.addPermissionListByUserIdAndResourceId(transUserId,transUserName, entityIds,MktTaskConstants.MKT_BID_ROLE_CODE);
    }

    /**
     *  将 JSON 字符串转换为 Map 对象，并从 Map 中获取 projectId 的值。
     * */
    private String jsonStrToMap(String bussesDataJson) {
        try {
            List<Map<String, String>> projectIdList = mapper.readValue(bussesDataJson, List.class);

            // 从List中获取第一个Map，并从中获取projectId的值
            if (CollectionUtils.isNotEmpty(projectIdList)&&projectIdList.size() == 1) {
                Map<String, String> firstElement = projectIdList.get(0);
                return firstElement.get(TaskConstants.PROJECT_ID);
            }
            log.error("bussesDataJson failed to transfer an ApprovalMaterialVo");
            throw new BusinessException(StatusCode.PARAM_ERROR);
        } catch (JsonProcessingException e) {
            log.error("bussesDataJson failed to transfer an ApprovalMaterialVo", e);
            throw new BusinessException(StatusCode.PARAM_ERROR);
        }
    }


    /**
     *  将key-value对转换为json字符串
     * */
    private String mapToJsonStr(String key,String value){
        List<Map<String, String>> projectList = new ArrayList<>();
        Map<String, String> projectMap = new HashMap<>();
        projectMap.put(key,value);
        projectList.add(projectMap);
        try {
            // 将List转换为JSON字符串
            return mapper.writeValueAsString(projectList);
        } catch (BusinessException | JsonProcessingException e) {
            log.error("failed to convert project list to JSON string:{}", e.getMessage(), e);
            throw new BusinessException(StatusCode.PARAM_ERROR);
        }
    }
    /* Ended by AICoder, pid:rcb4ck8c40qfc8e14ba00ad5b01076136592a0f0 */
}
