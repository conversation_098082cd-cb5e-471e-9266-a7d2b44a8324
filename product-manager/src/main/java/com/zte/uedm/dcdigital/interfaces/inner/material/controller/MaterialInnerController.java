/* Started by AICoder, pid:88ac6aa79awe4091428a08fff084de5dae8448db */
package com.zte.uedm.dcdigital.interfaces.inner.material.controller;

/* Started by AICoder, pid:l6116peb11ief1714ea3090d000ee09c1a9338ab */

import com.zte.uedm.dcdigital.application.material.DemandService;
import com.zte.uedm.dcdigital.application.material.MaterialMaintenanceCommandService;
import com.zte.uedm.dcdigital.application.material.MaterialMaintenanceQueryService;
import com.zte.uedm.dcdigital.common.bean.document.DocumentCitedVo;
import com.zte.uedm.dcdigital.common.bean.dto.ApprovalInformationDto;
import com.zte.uedm.dcdigital.common.bean.dto.MaterialDto;
import com.zte.uedm.dcdigital.common.bean.enums.ApprovalResult;
import com.zte.uedm.dcdigital.common.bean.enums.IdNameBean;
import com.zte.uedm.dcdigital.common.bean.product.MaterialWithExtendInfoVo;
import com.zte.uedm.dcdigital.common.enums.DocumentRelateResourceTypeEnum;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 物料维护内部接口控制器。
 * 该类定义了物料维护的内部调用接口，
 *
 * <AUTHOR>
 */
@Path("/material-inner")
@Api(value = "Internal material calling interface")
@Controller
@Slf4j
public class MaterialInnerController {


    @Autowired
    private MaterialMaintenanceCommandService materialMaintenanceCommandService;
    @Autowired
    private MaterialMaintenanceQueryService materialMaintenanceQueryService;
    @Autowired
    private DemandService demandService;
    //
    @POST
    @Path("/withdraw")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "撤回", notes = "审批中的物料经工作台撤回，调用该接口对物料进行更新", httpMethod = "POST")
    public BaseResult<Boolean> approvalWithdrawal(@QueryParam("approvalId") String approvalId) {
        if (StringUtils.isBlank(approvalId)) {
            log.error("The approval id:{} is empty.",approvalId);
            throw new BusinessException(StatusCode.PARAM_ERROR);
        }
        materialMaintenanceCommandService.withdrawByApprovalId(approvalId);
        return BaseResult.success();
    }

    @POST
    @Path("/update-after-approval")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "审批后更新", notes = "物料每经过一次审批都要对物料进行更新", httpMethod = "POST")
    public BaseResult<Boolean> updateAfterApproval(ApprovalInformationDto informationDto) {
        if (informationDto == null) {
            log.error("The request informationDto is empty.");
            throw new BusinessException(StatusCode.PARAM_ERROR);
        }
        //参数校验
        log.info("The request informationDto:{}",informationDto);
        parameterValidation(informationDto);
        //判断审批结果 是1-通过还是0-不通过 审批不通过和撤回是一样的
        if (!ApprovalResult.PASS.getCode().equals(informationDto.getApprovalResult())) {
            materialMaintenanceCommandService.withdrawByApprovalId(informationDto.getApprovalId());
        } else {
            //审批通过
            materialMaintenanceCommandService.approvalInformationUpdate(informationDto);
            //处理选型单
            List<MaterialDto> list = informationDto.getMaterial();
            for (MaterialDto me:list){
                demandService.updLectotypeMaterialStatus(me.getId(),2);
            }
        }
        return BaseResult.success();
    }

    /**
     * 参数校验。
     *
     * @param informationDto 审批信息DTO
     */
    private void parameterValidation(ApprovalInformationDto informationDto) {
        if (StringUtils.isBlank(informationDto.getApprovalId())) {
            log.error("The request ApprovalInformationDto approvalId is empty.");
            throw new BusinessException(StatusCode.PARAM_ERROR);
        }
        //判断审批结果 是1-通过还是0-不通过
        Integer approvalResult = informationDto.getApprovalResult();
        if (approvalResult == null || (!ApprovalResult.PASS.getCode().equals(approvalResult) && !ApprovalResult.REJECT.getCode().equals(approvalResult))) {
            log.error("The request ApprovalInformationDto approvalResult is null, or approvalResult outside the agreed range.");
            throw new BusinessException(StatusCode.PARAM_ERROR);
        }
        //审批状态 只有2-审批结束
        Integer approvalStatus = informationDto.getApprovalStatus();
        if (!GlobalConstants.APPROVAL_COMPLETED.equals(approvalStatus)) {
            log.error("The request ApprovalInformationDto approvalStatus:{} is error.",approvalStatus);
            throw new BusinessException(StatusCode.PARAM_ERROR);
        }
    }
    /* Ended by AICoder, pid:l6116peb11ief1714ea3090d000ee09c1a9338ab */

    @POST
    @Path("/select-cited-list")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "根据id列表查询物料", notes = "根据id列表查询物料", httpMethod = "POST")
    public BaseResult<List<DocumentCitedVo>> selectResourceInfo(@QueryParam("ids")List<String> ids,@QueryParam("type") Integer type) {
        log.info("ids==={},type={}",ids,type);
        List<DocumentCitedVo> result  = materialMaintenanceCommandService.selectResourceInfo(ids,type);
        log.info("result==={}",result);
        return BaseResult.success(result);
    }

    @POST
    @Path("/material/query-name-by-ids")
    @Produces({MediaType.APPLICATION_JSON})
    public BaseResult<List<IdNameBean>> queryNamesByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return BaseResult.success(Collections.emptyList());
        }
        List<DocumentCitedVo> list  = materialMaintenanceCommandService.selectResourceInfo(ids, DocumentRelateResourceTypeEnum.MATERIAL.getCode());
        List<IdNameBean> idNameList = list.stream().map((item) -> {
            IdNameBean idName = new IdNameBean();
            idName.setId(item.getId());
            idName.setName(item.getName());
            return idName;
        }).collect(Collectors.toList());
        return BaseResult.success(idNameList);
    }


    @POST
    @Path("/material/query-by-ids")
    @Produces({MediaType.APPLICATION_JSON})
    public BaseResult<List<MaterialWithExtendInfoVo>> queryMaterialByIds(@QueryParam("ids") List<String> ids) {
        List<MaterialWithExtendInfoVo> list = materialMaintenanceQueryService.queryMaterialByIds(ids);
        return BaseResult.success(list);
    }



}
/* Ended by AICoder, pid:88ac6aa79awe4091428a08fff084de5dae8448db */