package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.zte.uedm.dcdigital.infrastructure.repository.po.AssetFaqUpdPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper
public interface AssetFaqUpdMapper {

    void insert(AssetFaqUpdPo faqUpdPo);

    int selectUpdateNum(@Param("faqId") String faqId,@Param("todayStr") String todayStr);

    int selectCount(String productCategoryId, String dayStr);

    /**
     * 查询指定日期内faq的更新数
     */
    Long countUpdatedByDay(@Param("startDay") String startDay, @Param("endDay") String endDay);
}
