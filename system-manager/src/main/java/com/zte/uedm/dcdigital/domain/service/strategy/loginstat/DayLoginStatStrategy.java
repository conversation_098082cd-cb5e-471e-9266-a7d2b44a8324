/* Started by AICoder, pid:p833dl89ffh9c94142fd0aef32331b0bf6b2a4e9 */
package com.zte.uedm.dcdigital.domain.service.strategy.loginstat;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.AuthDeptEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.LoginStatDayEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.UserEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.DeptRepository;
import com.zte.uedm.dcdigital.domain.aggregate.repository.LoginStatDayRepository;
import com.zte.uedm.dcdigital.domain.aggregate.repository.UserRepository;
import com.zte.uedm.dcdigital.domain.common.constant.SystemConstants;
import com.zte.uedm.dcdigital.domain.service.DeptDomainService;
import com.zte.uedm.dcdigital.domain.utils.ParseUtils;
import com.zte.uedm.dcdigital.domain.utils.StatCurrentDateUtils;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.DeptUserRelationMapper;
import com.zte.uedm.dcdigital.interfaces.web.dto.LoginStatQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.AuthDeptUserVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.LoginStatisticsVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class DayLoginStatStrategy extends AbstractLoginStatStrategy implements LoginStatStrategy {
    @Autowired
    private LoginStatDayRepository dayRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private DeptRepository deptRepository;
    @Autowired
    private DeptDomainService deptDomainService;
    @Autowired
    private DeptUserRelationMapper deptUserRelationMapper;

    @Override
    public LoginStatisticsVo handle(LoginStatQueryDto dto) {
        LoginStatisticsVo loginStatisticsVo = new LoginStatisticsVo();
        //------------------基础数据统计-------------------
        //获取当前部门成员id
        List<String> deptUserIds = getDeptUserIds(dto.getDeptId());
        //查询所有部门信息
        AuthDeptEntity deptEntity = new AuthDeptEntity();
        List<AuthDeptEntity> deptVoList = deptRepository.queryDeptByCondition(deptEntity);
        List<String> leafIds = getLeafDept(dto.getDeptId(), deptVoList);
        //部门成员总数
        long deptAllUser = deptUserIds != null ? deptUserIds.size() : 0;
        LoginStatDayEntity dayEntity = new LoginStatDayEntity();
        //字符串转数字
        Integer beginTime = ParseUtils.parseStringToInt(dto.getStartTime());
        Integer endTime = ParseUtils.parseStringToInt(dto.getEndTime());
        dayEntity.setBeginTime(beginTime);
        dayEntity.setEndTime(endTime);
        //获取日表记录
        List<LoginStatDayEntity> loginStatDayList = dayRepository.getLoginStatDayList(dayEntity);
        List<LoginStatDayEntity> validList = loginStatDayList.stream()
                .filter(entity -> StringUtils.isNotBlank(entity.getDeptId()) && leafIds.contains(entity.getDeptId()))
                .collect(Collectors.toList());
        List<String> userIds = validList.stream().map(LoginStatDayEntity::getUserId).distinct().collect(Collectors.toList());
        List<Integer> dayList = validList.stream().map(LoginStatDayEntity::getDay).distinct().collect(Collectors.toList());

        Set<String> userIdSet = new HashSet<>();
        userIdSet.addAll(deptUserIds == null ? new ArrayList<>() : deptUserIds);
        userIdSet.addAll(userIds);
        List<String> allUserIds = new ArrayList<>(userIdSet);
        //获取部门成员姓名信息
        List<UserEntity> userEntities = userRepository.selectUserByIds(allUserIds);
        //转成key为userId,value为UserEntity的map集合
        Map<String, UserEntity> userEntityMap = userEntities.stream().collect(Collectors.toMap(UserEntity::getId, Function.identity()));
        //为filteredLoginStatDayList设置部门id路径
        setDepartmentIdPath(validList,LoginStatDayEntity::getDeptId, LoginStatDayEntity::setDeptPathId);
        //当前维度是"天"所以总登录天数就是日活跃用户数的总数
        //总登录天数
        long totalLoginDays = dayList.size();
        //活跃用户数(去重)
        long activeUser = userIds.size();
        //总用户数
        loginStatisticsVo.setTotalUser(deptAllUser);
        //总活跃用户数
        loginStatisticsVo.setActiveUser(activeUser);
        //人均登录天数（总登录天数/活跃用户数）
        loginStatisticsVo.setAvgLoginDays(calculateAvgLoginDays(totalLoginDays, activeUser));
        //活跃用户比例（活跃用户数/总人数）
        loginStatisticsVo.setActiveUserRate(calculateActiveUserRate(activeUser, deptAllUser));
        // 生成完整的时间区间列表
        List<Integer> fullDateRange = StatCurrentDateUtils.generateDayRange(beginTime, endTime);

        // 按 day 字段分组，生成 Map<day, List<LoginStatDayEntity>>
        Map<Integer, List<LoginStatDayEntity>> groupedByDay = validList.stream()
                .collect(Collectors.groupingBy(LoginStatDayEntity::getDay));
        assembleSystemStatistics(loginStatisticsVo, fullDateRange, groupedByDay,deptAllUser);
        //根据deptId获取部门叶子结点信息(只有叶子结点)
        List<AuthDeptUserVo> deptTrees = getDeptTreeByDeptId(dto.getDeptId());
        if (CollectionUtils.isNotEmpty(deptTrees) && deptTrees.stream().anyMatch(deptTree -> deptTree.getDeptId().equals(dto.getDeptId()))) {
            //确定是叶子结点展示如下数据
            assembleUserLogin(loginStatisticsVo, fullDateRange, groupedByDay, allUserIds, userEntityMap);
            return loginStatisticsVo;
        }
        assembleLevelDepart(loginStatisticsVo, fullDateRange, groupedByDay,dto.getDeptId());
        return loginStatisticsVo;
    }

    @Override
    public List<LoginStatisticsVo.UserLoginDetail> getExported(LoginStatQueryDto dto, HttpServletResponse response) {
        //根据deptId获取部门叶子结点信息(只有叶子结点)
        List<AuthDeptUserVo> deptTrees = getDeptTreeByDeptId(dto.getDeptId());
        if (CollectionUtils.isNotEmpty(deptTrees) && deptTrees.stream().anyMatch(deptTree -> deptTree.getDeptId().equals(dto.getDeptId()))) {
            //确定是叶子结点才能执行导出数据

            //获取当前部门成员id
            List<String> deptUserIds = getDeptUserIds(dto.getDeptId());
            //查询所有部门信息
            AuthDeptEntity deptEntity = new AuthDeptEntity();
            List<AuthDeptEntity> deptVoList = deptRepository.queryDeptByCondition(deptEntity);
            List<String> leafIds = getLeafDept(dto.getDeptId(), deptVoList);
            LoginStatDayEntity dayEntity = new LoginStatDayEntity();
            //字符串转数字
            Integer beginTime = ParseUtils.parseStringToInt(dto.getStartTime());
            Integer endTime = ParseUtils.parseStringToInt(dto.getEndTime());
            dayEntity.setBeginTime(beginTime);
            dayEntity.setEndTime(endTime);
            //获取日表记录
            List<LoginStatDayEntity> loginStatDayList = dayRepository.getLoginStatDayList(dayEntity);
            List<LoginStatDayEntity> validList = loginStatDayList.stream()
                    .filter(entity -> StringUtils.isNotBlank(entity.getDeptId()) && leafIds.contains(entity.getDeptId()))
                    .collect(Collectors.toList());
            List<String> userIds = validList.stream().map(LoginStatDayEntity::getUserId).distinct().collect(Collectors.toList());

            Set<String> userIdSet = new HashSet<>();
            userIdSet.addAll(deptUserIds == null ? new ArrayList<>() : deptUserIds);
            userIdSet.addAll(userIds);
            List<String> allUserIds = new ArrayList<>(userIdSet);

            //获取部门成员信息
            List<UserEntity> userEntities = userRepository.selectUserByIds(allUserIds);
            //转成key为userId,value为UserEntity的map集合
            Map<String, UserEntity> userEntityMap = userEntities.stream().collect(Collectors.toMap(UserEntity::getId, Function.identity()));
            // 生成完整的时间区间列表
            List<Integer> fullDateRange = StatCurrentDateUtils.generateDayRange(beginTime, endTime);


            // 按 day 字段分组，生成 Map<day, List<LoginStatDayEntity>>
            Map<Integer, List<LoginStatDayEntity>> groupedByDay = validList.stream()
                    .collect(Collectors.groupingBy(LoginStatDayEntity::getDay));
            LoginStatisticsVo loginStatisticsVo = new LoginStatisticsVo();
            assembleUserLogin(loginStatisticsVo, fullDateRange, groupedByDay, allUserIds, userEntityMap);
            return loginStatisticsVo.getUserLoginDetails();
        }
        return Collections.emptyList();
    }

    //---------------------全系统统计(天)----------------------------
    private void assembleSystemStatistics(LoginStatisticsVo loginStatisticsVo, List<Integer> fullDateRange, Map<Integer, List<LoginStatDayEntity>> groupedByDay,long deptAllUserNum) {
        List<LoginStatisticsVo.TotalSystem> totalSystemList = new ArrayList<>();
        //遍历完整时间区间
        for (Integer dateInt : fullDateRange) {
            LoginStatisticsVo.TotalSystem totalSystem = new LoginStatisticsVo.TotalSystem();
            //时间
            String day = ParseUtils.formatDay(dateInt);
            totalSystem.setTimePeriod(day);
            if (groupedByDay.containsKey(dateInt)) {
                List<LoginStatDayEntity> dayData = groupedByDay.getOrDefault(dateInt, new ArrayList<>());
                //去重统计活跃用户数
                List<String> userIds = dayData.stream()
                        .map(LoginStatDayEntity::getUserId)
                        .distinct()
                        .collect(Collectors.toList());
                long activeUserNum = userIds.size();
                totalSystem.setActiveUser(activeUserNum);
                //总登录天数等于活跃用户数
                long totalLoginDayNum = dayData.size();
                //人均登录天数（总登录天数/活跃用户数）
                totalSystem.setAvgLoginDays(calculateAvgLoginDays(totalLoginDayNum, activeUserNum));
                //活跃用户比例（活跃用户数/总人数）
                totalSystem.setActiveUserRate(calculateActiveUserRate(activeUserNum, deptAllUserNum));
            } else {
                totalSystem.setActiveUser(0);
                totalSystem.setAvgLoginDays(0);
                totalSystem.setActiveUserRate(0);
            }
            totalSystemList.add(totalSystem);
        }
        loginStatisticsVo.setTotalSystem(totalSystemList);
    }

    //--------------------下级部门统计(天)--------------------------
    private void assembleLevelDepart(LoginStatisticsVo loginStatisticsVo, List<Integer> fullDateRange, Map<Integer, List<LoginStatDayEntity>> groupedByDay, String deptId) {
        List<LoginStatisticsVo.LevelDepart> levelDepartList = new ArrayList<>();
        //获取直属下级部门名称
        Map<String, String> subDeptNameMap = getSubDeptMap(deptId);
        // 遍历直属下级部门
        for (Map.Entry<String, String> entry : subDeptNameMap.entrySet()) {
            //当前遍历的部门id
            String currentDeptId = entry.getKey();
            LoginStatisticsVo.LevelDepart levelDepartVo = new LoginStatisticsVo.LevelDepart();
            //部门活跃人数
            List<Integer> departActiveName = new ArrayList<>();
            //活跃用户比
            List<Integer> activeUserRate = new ArrayList<>();
            //时间
            List<String> timePeriod = new ArrayList<>();

            //当前遍历直属部门的用户总数
            int totalUserSum = getDeptUserIds(currentDeptId).size();
            // 遍历完整时间组装list数据
            for (Integer dateInt : fullDateRange) {
                //当前日期的活跃用户
                List<LoginStatDayEntity> dayData = groupedByDay.getOrDefault(dateInt, new ArrayList<>());
                //当前部门的活跃用户(根据subDeptNameMap.size()判断是直接聚合计算还是分开计算)
                long activeUserSum;
                if (subDeptNameMap.size() > 1) {
                    activeUserSum = dayData.stream()
                            .filter(Objects::nonNull)  // 过滤掉null元素
                            .filter(loginStatWeekEntity ->
                                    loginStatWeekEntity.getDeptPathId() != null &&  // 检查deptPathId不为null
                                            loginStatWeekEntity.getDeptPathId().contains(currentDeptId))
                            .map(LoginStatDayEntity::getUserId)
                            .filter(Objects::nonNull)  // 过滤掉null userId
                            .distinct()
                            .count();
                } else {
                    activeUserSum = dayData.size();
                }
                //时间周期
                timePeriod.add(ParseUtils.formatDay(dateInt));
                //活跃用户
                departActiveName.add((int) activeUserSum);
                //活跃用户比例
                activeUserRate.add((int) calculateActiveUserRate(activeUserSum, totalUserSum));
            }
            levelDepartVo.setDepartName(subDeptNameMap.get(entry.getKey()));
            levelDepartVo.setActiveUserRate(activeUserRate);
            levelDepartVo.setTimePeriod(timePeriod);
            levelDepartVo.setDepartActiveName(departActiveName);
            levelDepartList.add(levelDepartVo);
        }
        levelDepartList.sort(Comparator.comparing(LoginStatisticsVo.LevelDepart::getDepartName, Comparator.nullsLast(String::compareTo)));
        loginStatisticsVo.setLevelDepart(levelDepartList);
    }

    //---------------------用户登录统计(天)--------------------------
    private void assembleUserLogin(LoginStatisticsVo loginStatisticsVo, List<Integer> fullDateRange, Map<Integer, List<LoginStatDayEntity>> groupedByDay, List<String> deptUserIds, Map<String, UserEntity> userEntityMap) {
        List<LoginStatisticsVo.UserLoginDetail> userLoginDetails = new ArrayList<>();
        //遍历总用户
        if (CollectionUtils.isNotEmpty(deptUserIds)) {
            for (String userId : deptUserIds) {
                LoginStatisticsVo.UserLoginDetail userLoginDetail = new LoginStatisticsVo.UserLoginDetail();
                List<Map<String, Long>> listMap = new ArrayList<>();
                //遍历遍历完整时间区间组装map
                long loginSum = SystemConstants.ZERO;
                for (Integer dateInt : fullDateRange) {
                    Map<String, Long> map = new LinkedHashMap<>();
                    //当遍历的用户id存在当前循环日期中时则记为登录
                    if (groupedByDay.containsKey(dateInt) && groupedByDay.getOrDefault(dateInt, new ArrayList<>()).stream().anyMatch(entity -> entity.getUserId().equals(userId))) {
                        map.put(ParseUtils.formatDayZn(dateInt), (long) SystemConstants.ONE);
                        loginSum++;
                    } else {
                        map.put(ParseUtils.formatDayZn(dateInt), (long) SystemConstants.ZERO);
                    }
                    listMap.add(map);
                }
                userLoginDetail.setPeriodLoginStatTimes(listMap);
                userLoginDetail.setUserName(userEntityMap.getOrDefault(userId, new UserEntity()).getName());
                //总次数
                userLoginDetail.setTotal(loginSum);
                userLoginDetails.add(userLoginDetail);
            }
        }
        userLoginDetails.sort(Comparator.comparing(LoginStatisticsVo.UserLoginDetail::getTotal).reversed());
        loginStatisticsVo.setUserLoginDetails(userLoginDetails);
    }
}

/* Ended by AICoder, pid:p833dl89ffh9c94142fd0aef32331b0bf6b2a4e9 */