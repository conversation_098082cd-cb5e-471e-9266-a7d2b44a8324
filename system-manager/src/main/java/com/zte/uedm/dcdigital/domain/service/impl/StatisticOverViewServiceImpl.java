package com.zte.uedm.dcdigital.domain.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.zte.uedm.dcdigital.application.stat.executor.BusinessStatService;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.*;
import com.zte.uedm.dcdigital.domain.service.LoginStatsDomainService;
import com.zte.uedm.dcdigital.domain.service.StatisticOverViewService;
import com.zte.uedm.dcdigital.domain.utils.DocAssetTimeUtils;
import com.zte.uedm.dcdigital.domain.utils.ParseUtils;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.*;
import com.zte.uedm.dcdigital.infrastructure.repository.po.*;
import com.zte.uedm.dcdigital.interfaces.web.dto.LoginQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class StatisticOverViewServiceImpl implements StatisticOverViewService {

    @Autowired
    private LoginStatsDomainService loginStatsDomainService;

    @Autowired
    private StatOverviewMapper statOverviewMapper;

    @Autowired
    private SearchStatDayMapper searchStatDayMapper;

    @Autowired
    private SearchStatWeekMapper searchStatWeekMapper;

    @Autowired
    private SearchStatMonthMapper searchStatMonthMapper;

    @Autowired
    private SearchStatYearMapper searchStatYearMapper;

    @Autowired
    private IntelligenceStatDayMapper intelligenceStatDayMapper;

    @Autowired
    private IntelligenceStatWeekMapper intelligenceStatWeekMapper;

    @Autowired
    private IntelligenceStatMonthMapper intelligenceStatMonthMapper;

    @Autowired
    private IntelligenceStatYearMapper intelligenceStatYearMapper;

    @Autowired
    private DocDownStatDayMapper docDownStatDayMapper;

    @Autowired
    private DocDownStatMonthMapper docDownStatMonthMapper;

    @Autowired
    private DocDownStatWeekMapper docDownStatWeekMapper;

    @Autowired
    private DocDownStatYearMapper docDownStatYearMapper;

    @Autowired
    private DeptUserRelationMapper deptUserRelationMapper;

    @Autowired
    private BusinessStatService businessStatService;

    @Autowired
    private DocAssetDayMapper docAssetDayMapper;

    @Autowired
    private DocAssetWeekMapper docAssetWeekMapper;

    @Autowired
    private DocAssetMonthMapper docAssetMonthMapper;

    @Autowired
    private DocAssetYearMapper docAssetYearMapper;
    @Autowired
    private DocAssetMapper docAssetMapper;

    @Autowired
    private AssetFaqDayMapper assetFaqDayMapper;

    @Autowired
    private AssetFaqWeekMapper assetFaqWeekMapper;

    @Autowired
    private AssetFaqMonthMapper assetFaqMonthMapper;

    @Autowired
    private AssetFaqYearMapper assetFaqYearMapper;
    @Autowired
    private AssetFaqUpdMapper assetFaqUpdMapper;

    @Autowired
    private MaterialAssetMapper materialAssetMapper;
    @Autowired
    private MaterialAssetUpdMapper materialAssetUpdMapper;
    @Autowired
    private BusinessAssetMapper businessAssetMapper;
    @Autowired
    private PageStatMapper pageStatMapper;

    @Override
    public StatisticOverviewVo statisticOverView(LoginQueryDto loginQueryDto) {
        String startTime = loginQueryDto.getStartTime();
        String endTime = loginQueryDto.getEndTime();
        Integer timeType = loginQueryDto.getTimeType();

        // 登录统计 活跃用户数 活跃用户比 人均登录数
        StatisticOverviewVo statisticOverviewVo = statisticsUser(startTime, endTime, timeType);
        long activeUsers = statisticOverviewVo.getActiveUser();

        // 访问统计 1品牌 2物料 3文档 4FAQ
        statisticOverviewVo.getPageStatistics().addAll(
                statModule("1", startTime, endTime, timeType, activeUsers));
        statisticOverviewVo.getPageStatistics().addAll(
                statModule("2", startTime, endTime, timeType, activeUsers));
        statisticOverviewVo.getPageStatistics().addAll(
                statModule("3", startTime, endTime, timeType, activeUsers));
        statisticOverviewVo.getPageStatistics().addAll(
                statModule("4", startTime, endTime, timeType, activeUsers));


        // 搜索统计
        statisticOverviewVo.setSearchStatistics(searchStatistics(startTime, endTime, timeType, activeUsers));
        // 文档
        statisticOverviewVo.setFileStatistics(statFile(startTime, endTime, timeType, activeUsers));
        // 智能体
        statisticOverviewVo.setIntelligenceStatistics(staticsIntelligence(startTime, endTime, timeType, activeUsers));
        //资产统计
        statisticOverviewVo.setAssetStatistics(staticsAsset(startTime, endTime, timeType));
        //更新统计
        statisticOverviewVo.setUpdStatistics(searchStatistics(statisticOverviewVo));

        //业务统计
        statisticOverviewVo.setBusinessStatisticVo(businessStatService.overviewStatBusiness(startTime, endTime, timeType));

        return statisticOverviewVo;
    }

    /* Started by AICoder, pid:r3f54o55b351725143d809bf801d764529a61247 */
    private List<BaseStatisticsVo> statModule(String module, String startTime, String endTime, Integer timeType, long activeUsers) {

        List<StatOverviewPagePo> statOverviewPagePoList = new ArrayList<>();
        StatOverviewPagePo statOverviewPagePo = new StatOverviewPagePo();
        statOverviewPagePo.setStartTime(Integer.parseInt(startTime));
        statOverviewPagePo.setEndTime(Integer.parseInt(endTime));
        statOverviewPagePo.setType(module);
        switch (timeType) {
            case 1: // 天
                statOverviewPagePoList.addAll(statOverviewMapper.selectPageStatDayList(statOverviewPagePo));
                break;
            case 2: // 周
                statOverviewPagePoList.addAll(statOverviewMapper.selectPageStatWeekList(statOverviewPagePo));
                break;
            case 3: // 月
                statOverviewPagePoList.addAll(statOverviewMapper.selectPageStatMonthList(statOverviewPagePo));
                break;
            case 4: // 年
                statOverviewPagePoList.addAll(statOverviewMapper.selectPageStatYearList(statOverviewPagePo));
                break;
            default:
                log.error("timeType:{} invalid", timeType);
                return Collections.emptyList();
        }


        long totalVisits = 0L;
        for (StatOverviewPagePo Po: statOverviewPagePoList) {
            totalVisits += Integer.parseInt(Po.getNum());
        }
        long avgVisits = calculateAverage(totalVisits, activeUsers);
        log.info("statVisitsDataByDay timeType:{}, module:{}, totalVisits:{}, avgVisits:{}, user:{}", timeType, module, totalVisits, avgVisits, activeUsers);

        List<BaseStatisticsVo> pageStatistics = new ArrayList<>();
        pageStatistics.add(new BaseStatisticsVo(totalVisits, avgVisits, module));
        return pageStatistics;
    }
    /* Ended by AICoder, pid:r3f54o55b351725143d809bf801d764529a61247 */

    private StatisticOverviewVo statisticsUser(String startTime, String endTime, Integer timeType ) {
        List<LoginStatDayPo> loginPos = pageStatMapper.queryLoginByType(
                ParseUtils.getBeginDay(Integer.parseInt(startTime), timeType),
                ParseUtils.getEndDay(Integer.parseInt(endTime), timeType),
                1
        );
        long howManyDays = loginPos.stream().map(LoginStatDayPo::getDay).distinct().count();
        // 活跃用户数（有登录的用户）
        long activeUser = loginPos.stream().map(LoginStatDayPo::getUserId).distinct().count();

        // 总用户数
        long totalUser = pageStatMapper.getSystemUserCount();

        // 活跃用户比
        double activeUserRate = totalUser == 0 ? 0 : (double) activeUser / totalUser;
        activeUserRate = new BigDecimal(activeUserRate).setScale(2, RoundingMode.HALF_UP).doubleValue();

        // 平均登录天数（所有用户的登录天数总和/总用户数）
        long avgLoginDays = activeUser == 0 ? 0 : howManyDays / activeUser;

        // 8. 封装VO返回
        StatisticOverviewVo overview = new StatisticOverviewVo();
        overview.setActiveUser(activeUser);
        overview.setActiveUserRate(activeUserRate);
        overview.setAvgLoginDays(avgLoginDays);

        return overview;
    }
    /* Started by AICoder, pid:33d160cc99fca1a1457c0bd61092435e61f1f0f4 */
    public List<BaseStatisticsVo> searchStatistics(String startTime, String endTime, Integer timeType, long activeUsers) {
        switch (timeType){
            case 1:
                return statSearchByDay(startTime, endTime, activeUsers);
            case 2:
                return statSearchByWeek(startTime, endTime, activeUsers);
            case 3:
                return statSearchByMonth(startTime, endTime, activeUsers);
            case 4:
                return statSearchYear(startTime, endTime, activeUsers);
            default:
                // 默认情况或错误处理
                throw new BusinessException(StatusCode.PARAM_ERROR);
        }
    }

    private List<BaseStatisticsVo> statSearchByDay(String startTime, String endTime, long activeUsers) {
        /* Started by AICoder, pid:82dedwbbfeic7c414a7808d9e0ec560326e9d110 */
        List<SearchStatDayPo> statDayPoList = searchStatDayMapper.aggregateByDateRange(startTime, endTime);
        List<SearchStatDayEntity> list =
          statDayPoList.parallelStream()
                .map(po -> {
                    SearchStatDayEntity entity = new SearchStatDayEntity();
                    BeanUtils.copyProperties(po, entity);
                    return entity;
                })
                .collect(Collectors.toList());
        /* Ended by AICoder, pid:82dedwbbfeic7c414a7808d9e0ec560326e9d110 */
        return statSearch(list, activeUsers);
    }
    private List<BaseStatisticsVo> statSearchByWeek(String startTime, String endTime, long activeUsers) {
        List<SearchStatWeekPo> statWeekPoList = searchStatWeekMapper.selectByTimeRange(startTime, endTime);
        List<SearchStatDayEntity> list = statWeekPoList.parallelStream().map(po->{
            SearchStatDayEntity entity = new SearchStatDayEntity();
            BeanUtils.copyProperties(po, entity);
            return entity;
        }).collect(Collectors.toList());
        return statSearch(list, activeUsers);
    }
    private List<BaseStatisticsVo> statSearchByMonth(String startTime, String endTime, long activeUsers) {
        List<SearchStatMonthPo> statMonthPoList = searchStatMonthMapper.selectByTimeRange(startTime, endTime);
        List<SearchStatDayEntity> list = statMonthPoList.parallelStream().map(po->{
            SearchStatDayEntity entity = new SearchStatDayEntity();
            BeanUtils.copyProperties(po, entity);
            return entity;
        }).collect(Collectors.toList());
        return statSearch(list, activeUsers);
    }
    private List<BaseStatisticsVo> statSearchYear(String startTime, String endTime, long activeUsers) {
        List<SearchStatYearPo> statYearPoList = searchStatYearMapper.selectByTimeRange(startTime, endTime);
        List<SearchStatDayEntity> list = statYearPoList.parallelStream().map(po->{
            SearchStatDayEntity entity = new SearchStatDayEntity();
            BeanUtils.copyProperties(po, entity);
            return entity;
        }).collect(Collectors.toList());
        return statSearch(list, activeUsers);
    }

    /* Started by AICoder, pid:g57b58de1bgfa8b1423d0996c061de4b5f52e8c3 */
    private List<BaseStatisticsVo> statSearch( List<SearchStatDayEntity> list, long activeUsers){
        List<BaseStatisticsVo> res = new ArrayList<>();
        BaseStatisticsVo material = new BaseStatisticsVo(0,0,"2");
        BaseStatisticsVo file = new BaseStatisticsVo(0,0,"3");
        BaseStatisticsVo faq = new BaseStatisticsVo(0,0,"4");
        res.add(material);
        res.add(file);
        res.add(faq);
        if (CollectionUtils.isEmpty(list)) {
            return res;
        }
        long materialTotal = 0L;
        long fileTotal = 0L;
        long faqTotal = 0L;
        for (SearchStatDayEntity bean : list) {
            materialTotal += Integer.parseInt(bean.getMaterialNum());
            fileTotal += Integer.parseInt(bean.getFileNum());
            faqTotal += Integer.parseInt(bean.getFaqNum());
        }

        material.setTotal(materialTotal);
        material.setAvg(calculateAverage(materialTotal, activeUsers));

        file.setTotal(fileTotal);
        file.setAvg(calculateAverage(fileTotal, activeUsers));

        faq.setTotal(faqTotal);
        faq.setAvg(calculateAverage(faqTotal, activeUsers));
        return res;
    }
    /* Ended by AICoder, pid:g57b58de1bgfa8b1423d0996c061de4b5f52e8c3 */
    /* Ended by AICoder, pid:33d160cc99fca1a1457c0bd61092435e61f1f0f4 */

    /* Started by AICoder, pid:u650d334587e1f7141bc0841906c0074bb223f22 */
    public FileStatisticsVo statFile(String startTime, String endTime, Integer timeType, long activeUsers) {
        switch (timeType){
            case 1:
                return statFileByDay(startTime, endTime);
            case 2:
                return statFileByWeek(startTime, endTime);
            case 3:
                return statFileByMonth(startTime, endTime);
            case 4:
                return statFileYear(startTime, endTime);
            default:
                // 默认情况或错误处理
                throw new BusinessException(StatusCode.PARAM_ERROR);
        }
    }

    private FileStatisticsVo statFileByDay(String startTime, String endTime) {
        List<DocDownStatDayPo> statDayPoList = docDownStatDayMapper.selectByTimeRange(Integer.parseInt(startTime),Integer.parseInt(endTime));
        List<DocDownStatDayEntity> list = statDayPoList.parallelStream().map(po->{
            DocDownStatDayEntity entity = new DocDownStatDayEntity();
            BeanUtils.copyProperties(po, entity);
            return entity;
        }).collect(Collectors.toList());
        return statisticsFile(list);
    }

    private FileStatisticsVo statFileByWeek(String startTime, String endTime) {
        List<DocDownStatWeekPo> statWeekPoList = docDownStatWeekMapper.selectByTimeRange(Integer.parseInt(startTime),Integer.parseInt(endTime));
        List<DocDownStatDayEntity> list = statWeekPoList.parallelStream().map(po->{
            DocDownStatDayEntity entity = new DocDownStatDayEntity();
            BeanUtils.copyProperties(po, entity);
            return entity;
        }).collect(Collectors.toList());
        return statisticsFile(list);
    }

    private FileStatisticsVo statFileByMonth(String startTime, String endTime) {
        List<DocDownStatMonthPo> statMonthPoList = docDownStatMonthMapper.selectByTimeRange(Integer.parseInt(startTime),Integer.parseInt(endTime));
        List<DocDownStatDayEntity> list = statMonthPoList.parallelStream().map(po->{
            DocDownStatDayEntity entity = new DocDownStatDayEntity();
            BeanUtils.copyProperties(po, entity);
            return entity;
        }).collect(Collectors.toList());
        return statisticsFile(list);
    }

    private FileStatisticsVo statFileYear(String startTime, String endTime) {
        List<DocDownStatYearPo> statYearPoList = docDownStatYearMapper.selectByTimeRange(Integer.parseInt(startTime),Integer.parseInt(endTime));
        List<DocDownStatDayEntity> list = statYearPoList.parallelStream().map(po->{
            DocDownStatDayEntity entity = new DocDownStatDayEntity();
            BeanUtils.copyProperties(po, entity);
            return entity;
        }).collect(Collectors.toList());
        return statisticsFile(list);
    }


    private FileStatisticsVo statisticsFile(List<DocDownStatDayEntity> list){
        FileStatisticsVo vo = new FileStatisticsVo();
        if (CollectionUtils.isEmpty(list)){
            return vo;
        }
        int userCount = deptUserRelationMapper.selectAllDeptUserRelation().size();

        long totalDownLoad = 0L;
        long totalPreview = 0L;

        // 合并计算总和
        for (DocDownStatDayEntity bean : list) {
            totalDownLoad += bean.getDownloadNum();
            totalPreview += bean.getPreviewNum();
        }
        vo.setTotalDownLoad(totalDownLoad);
        vo.setTotalPreview(totalPreview);
        vo.setAvgDownLoad(calculateAverage(totalDownLoad, userCount));
        vo.setAvgPreview(calculateAverage(totalPreview, userCount));
        return vo;
    }
    /* Ended by AICoder, pid:u650d334587e1f7141bc0841906c0074bb223f22 */

    /* Started by AICoder, pid:i6bd4e5c42pe74c1485b091180a7bc31fdb76d85 */
    public IntelligenceStatisticsVo  staticsIntelligence(String startTime, String endTime, Integer timeType, long activeUsers){
        switch (timeType){
            case 1:
                return staticsIntelligenceByDay(startTime, endTime);
            case 2:
                return staticsIntelligenceByWeek(startTime, endTime);
            case 3:
                return staticsIntelligenceByMonth(startTime, endTime);
            case 4:
                return staticsIntelligenceByYear(startTime, endTime);
            default:
                // 默认情况或错误处理
                throw new BusinessException(StatusCode.PARAM_ERROR);
        }
    }

    private IntelligenceStatisticsVo staticsIntelligenceByDay(String startTime, String endTime) {
        List<IntelligenceStatDayPo> statDayPoList = intelligenceStatDayMapper.aggregateByDateRange(startTime, endTime);
        List<IntelligenceStatDayEntity> list = statDayPoList.parallelStream().map(po->{
            IntelligenceStatDayEntity entity = new IntelligenceStatDayEntity();
            BeanUtils.copyProperties(po, entity);
            return entity;
        }).collect(Collectors.toList());

        return staticsIntelligence(list);
    }

    private IntelligenceStatisticsVo staticsIntelligenceByWeek(String startTime, String endTime) {
        List<IntelligenceStatWeekPo> statWeekPoList = intelligenceStatWeekMapper.selectByTimeRange(startTime, endTime);
        List<IntelligenceStatDayEntity> list = statWeekPoList.parallelStream().map(po -> {
            IntelligenceStatDayEntity entity = new IntelligenceStatDayEntity();
            BeanUtils.copyProperties(po, entity);
            return entity;
        }).collect(Collectors.toList());
        return staticsIntelligence(list);
    }

    private IntelligenceStatisticsVo staticsIntelligenceByMonth(String startTime, String endTime) {
        List<IntelligenceStatMonthPo> statMonthPoList = intelligenceStatMonthMapper.selectByTimeRange(startTime, endTime);
        List<IntelligenceStatDayEntity> list = statMonthPoList.parallelStream().map(po -> {
            IntelligenceStatDayEntity entity = new IntelligenceStatDayEntity();
            BeanUtils.copyProperties(po, entity);
            return entity;
        }).collect(Collectors.toList());
        return staticsIntelligence(list);
    }

    private IntelligenceStatisticsVo staticsIntelligenceByYear(String startTime, String endTime) {
        List<IntelligenceStatYearPo> statYearPoList = intelligenceStatYearMapper.selectByTimeRange(startTime, endTime);
        List<IntelligenceStatDayEntity> list = statYearPoList.parallelStream().map(po -> {
            IntelligenceStatDayEntity entity = new IntelligenceStatDayEntity();
            BeanUtils.copyProperties(po, entity);
            return entity;
        }).collect(Collectors.toList());
        return staticsIntelligence(list);
    }
    /* Ended by AICoder, pid:i6bd4e5c42pe74c1485b091180a7bc31fdb76d85 */


    /* Started by AICoder, pid:q33d1j4346bdd3e1453709d04081504994525149 */
    private IntelligenceStatisticsVo staticsIntelligence(List<IntelligenceStatDayEntity> list) {
        IntelligenceStatisticsVo vo = new IntelligenceStatisticsVo();
        if (CollectionUtils.isEmpty(list)) {
            return vo;
        }

        int userCount = deptUserRelationMapper.selectAllDeptUserRelation().size();

        long totalMaterial = 0L;
        long totalBid = 0L;

        // 合并计算总和
        for (IntelligenceStatDayEntity bean : list) {
            totalMaterial += Integer.parseInt(bean.getMaterialNum());
            totalBid += Integer.parseInt(bean.getBidNum());
        }

        vo.setTotalMaterial(totalMaterial);
        vo.setTotalBid(totalBid);
        vo.setTotal(totalMaterial + totalBid);
        vo.setAvg(calculateAverage(totalMaterial + totalBid, userCount));

        vo.setAvgMaterial( calculateAverage(totalMaterial, userCount));
        vo.setAvgBid(calculateAverage(totalBid, userCount));

        return vo;
    }
    /* Started by AICoder, pid:e3c53x45efm3c09147e20b4db0cf4234e6534571 */
    public List<BaseStatisticsVo> searchStatistics(StatisticOverviewVo statisticOverviewVo){
        AssetStatisticsVo assetStatisticsVo = statisticOverviewVo.getAssetStatistics();
        int activeUser = (int) statisticOverviewVo.getActiveUser();
        List<BaseStatisticsVo> res = new ArrayList<>();
        BaseStatisticsVo material = new BaseStatisticsVo(0,0,"2");
        BaseStatisticsVo file = new BaseStatisticsVo(0,0,"3");
        BaseStatisticsVo faq = new BaseStatisticsVo(0,0,"4");
        res.add(material);
        res.add(file);
        res.add(faq);

        material.setTotal(assetStatisticsVo.getMaterialUpdateNum());
        material.setAvg(calculateAverage(assetStatisticsVo.getMaterialUpdateNum(), activeUser));

        file.setTotal(assetStatisticsVo.getDocUpdateNum());
        file.setAvg(calculateAverage(assetStatisticsVo.getDocUpdateNum(), activeUser));

        faq.setTotal(assetStatisticsVo.getFaqUpdateNum());
        faq.setAvg(calculateAverage(assetStatisticsVo.getFaqUpdateNum(), activeUser));
        return res;
    }
    /* Ended by AICoder, pid:e3c53x45efm3c09147e20b4db0cf4234e6534571 */
    /* Started by AICoder, pid:g69d4q0a79747b41493b0b5c70c7e85b9865249e */
    public AssetStatisticsVo  staticsAsset(String startTime, String endTime, Integer timeType){
        switch (timeType){
            case 1:
                return staticsAssetByDay(startTime, endTime);
            case 2:
                return staticsAssetByWeek(startTime, endTime);
            case 3:
                return staticsAssetByMonth(startTime, endTime);
            case 4:
                return staticsAssetByYear(startTime, endTime);
            default:
                // 默认情况或错误处理
                throw new BusinessException(StatusCode.PARAM_ERROR);
        }
    }
    /* Ended by AICoder, pid:g69d4q0a79747b41493b0b5c70c7e85b9865249e */
    /* Started by AICoder, pid:z7e1803afbqdc8d147e10991e0e5fc97deb074f4 */
    private AssetStatisticsVo staticsAssetByDay(String startTime, String endTime) {
        AssetStatisticsVo assetStatisticsVo = new AssetStatisticsVo();
        //物料信息
        List<MaterialAssetDayEntity> entities = materialAssetMapper.selectDayDateByTimeRange(startTime, endTime);
        if(!entities.isEmpty()){
            assetStatisticsVo.setMaterialTotal(entities.get(0).getAllNum());
            assetStatisticsVo.setUpChangeNum(entities.get(0).getUpChangeNum());
            assetStatisticsVo.setDownChangeNum(entities.get(0).getDownChangeNum());
            long upNum = 0L;
            for (MaterialAssetDayEntity data : entities) {
                upNum += data.getUpNum();
            }
            assetStatisticsVo.setUpNum(upNum);
            Long materialUpdateNum = materialAssetUpdMapper.countUpdatedByDay(startTime, endTime);
            assetStatisticsVo.setMaterialUpdateNum(materialUpdateNum);
        }
        //faq信息
        List<AssetFaqDayPo> poList = assetFaqDayMapper.selectDateByTimeRange(startTime, endTime);
        if(!poList.isEmpty()){
            // 计算数量信息
            Map<String, List<AssetFaqDayPo>> dayMap = poList.stream()
                    .collect(Collectors.groupingBy(AssetFaqDayPo::getDay));
            List<FaqStatisticVo.FaqStatDataVo> faqStatDataVos = dayMap.entrySet().stream()
                    .map(entry -> {
                        String day = entry.getKey();
                        List<AssetFaqDayPo> dataList = entry.getValue();
                        long allNum = dataList.stream()
                                .mapToLong(data -> Long.parseLong(data.getAllNum())).sum();
                        long updateNum = entry.getValue().stream()
                                .mapToLong(data -> Long.parseLong(data.getUpdateNum())).sum();
                        long changeNum = entry.getValue().stream()
                                .mapToLong(data -> Long.parseLong(data.getChangeNum())).sum();
                        return new FaqStatisticVo.FaqStatDataVo(day, allNum, updateNum, changeNum);
                    })
                    .sorted((a, b) -> Long.compare(Long.parseLong(b.getDay()), Long.parseLong(a.getDay())))
                    .collect(Collectors.toList());
            assetStatisticsVo.setFaqTotal(faqStatDataVos.get(0).getAllNum());
            assetStatisticsVo.setFaqChangeNum(faqStatDataVos.get(0).getChangeNum());
            Long faqUpdateNum = assetFaqUpdMapper.countUpdatedByDay(startTime, endTime);
            assetStatisticsVo.setFaqUpdateNum(faqUpdateNum);
        }
        //文档信息
        List<DocAssetEntity> docAssetEntities = docAssetDayMapper.selectByTimeRange(Integer.parseInt(startTime), Integer.parseInt(endTime));
        if(!docAssetEntities.isEmpty()){
            assetStatisticsVo.setDocTotal(docAssetEntities.get(0).getAllNum());
            assetStatisticsVo.setDocChangeNum(docAssetEntities.get(0).getChangeNum());
            Long docUpdateNum = docAssetMapper.countUpdatedByDay(Integer.parseInt(startTime), Integer.parseInt(endTime));
            assetStatisticsVo.setDocUpdateNum(docUpdateNum);
        }
        //商机信息
        /* Started by AICoder, pid:500d9y38a5498c114bb40898f00b762e7f27e887 */
        List<BusinessAssetDayEntity> businessEntities = businessAssetMapper.selectDayDateByTimeRange(startTime, endTime);
        if(!businessEntities.isEmpty()){
            long projectAddNum = 0L;
            long projectStartNum = 0L;
            for (BusinessAssetDayEntity entity : businessEntities) {
                projectAddNum += entity.getProjectAddNum();
                projectStartNum += entity.getProjectStartNum();
            }
            assetStatisticsVo.setProjectAddNum(projectAddNum);
            assetStatisticsVo.setProjectStartNum(projectStartNum);
        }
        /* Ended by AICoder, pid:500d9y38a5498c114bb40898f00b762e7f27e887 */
        return assetStatisticsVo;
    }
    /* Ended by AICoder, pid:z7e1803afbqdc8d147e10991e0e5fc97deb074f4 */

    /* Started by AICoder, pid:0fec5t1bbcxbf3a14c100aa4e02bab8ac14008a2 */
    private AssetStatisticsVo staticsAssetByWeek(String startTime, String endTime) {
        //获得当前周的周一和周日
        int mondayOfWeek = DocAssetTimeUtils.getMondayOfWeek(Integer.parseInt(startTime));
        int sundayOfWeek = DocAssetTimeUtils.getSundayOfWeek(Integer.parseInt(endTime));
        AssetStatisticsVo assetStatisticsVo = new AssetStatisticsVo();
        //物料信息
        List<MaterialAssetWeekEntity> entities = materialAssetMapper.selectWeekDateByTimeRange(startTime, endTime);
        if(!entities.isEmpty()){
            assetStatisticsVo.setMaterialTotal(entities.get(0).getAllNum());
            assetStatisticsVo.setUpChangeNum(entities.get(0).getUpChangeNum());
            assetStatisticsVo.setDownChangeNum(entities.get(0).getDownChangeNum());
            long upNum = 0L;
            for (MaterialAssetWeekEntity data : entities) {
                upNum += data.getUpNum();
            }
            assetStatisticsVo.setUpNum(upNum);
            Long materialUpdateNum = materialAssetUpdMapper.countUpdatedByDay(String.valueOf(mondayOfWeek), String.valueOf(sundayOfWeek));
            assetStatisticsVo.setMaterialUpdateNum(materialUpdateNum);
        }
        //faq信息
        List<AssetFaqWeekPo> poList = assetFaqWeekMapper.selectDateByTimeRange(startTime, endTime);
        if(!poList.isEmpty()){
            // 计算数量信息
            Map<String, List<AssetFaqWeekPo>> dayMap = poList.stream()
                    .collect(Collectors.groupingBy(AssetFaqWeekPo::getDay));
            List<FaqStatisticVo.FaqStatDataVo> faqStatDataVos = dayMap.entrySet().stream()
                    .map(entry -> {
                        String day = entry.getKey();
                        List<AssetFaqWeekPo> dataList = entry.getValue();
                        long allNum = dataList.stream()
                                .mapToLong(data -> Long.parseLong(data.getAllNum())).sum();
                        long updateNum = entry.getValue().stream()
                                .mapToLong(data -> Long.parseLong(data.getUpdateNum())).sum();
                        long changeNum = entry.getValue().stream()
                                .mapToLong(data -> Long.parseLong(data.getChangeNum())).sum();
                        return new FaqStatisticVo.FaqStatDataVo(day, allNum, updateNum, changeNum);
                    })
                    .sorted((a, b) -> Long.compare(Long.parseLong(b.getDay()), Long.parseLong(a.getDay())))
                    .collect(Collectors.toList());
            assetStatisticsVo.setFaqTotal(faqStatDataVos.get(0).getAllNum());
            assetStatisticsVo.setFaqChangeNum(faqStatDataVos.get(0).getChangeNum());
            Long faqUpdateNum = assetFaqUpdMapper.countUpdatedByDay(String.valueOf(mondayOfWeek), String.valueOf(sundayOfWeek));
            assetStatisticsVo.setFaqUpdateNum(faqUpdateNum);
        }
        //文档信息
        List<DocAssetEntity> docAssetEntities = docAssetWeekMapper.selectByTimeRange(Integer.parseInt(startTime), Integer.parseInt(endTime));
        if(!docAssetEntities.isEmpty()){
            assetStatisticsVo.setDocTotal(docAssetEntities.get(0).getAllNum());
            assetStatisticsVo.setDocChangeNum(docAssetEntities.get(0).getChangeNum());
            Long docUpdateNum = docAssetMapper.countUpdatedByDay(mondayOfWeek, sundayOfWeek);
            assetStatisticsVo.setDocUpdateNum(docUpdateNum);
        }
        //商机信息
        /* Started by AICoder, pid:i2572scba39a2fd14f770a172014d22d63276aa3 */
        List<BusinessAssetWeekEntity> businessEntities = businessAssetMapper.selectWeekDateByTimeRange(startTime, endTime);
        if(!businessEntities.isEmpty()){
            long projectAddNum = 0L;
            long projectStartNum = 0L;
            for (BusinessAssetWeekEntity entity : businessEntities) {
                projectAddNum += entity.getProjectAddNum();
                projectStartNum += entity.getProjectStartNum();
            }
            assetStatisticsVo.setProjectAddNum(projectAddNum);
            assetStatisticsVo.setProjectStartNum(projectStartNum);
        }
        /* Ended by AICoder, pid:i2572scba39a2fd14f770a172014d22d63276aa3 */
        return assetStatisticsVo;
    }
    /* Ended by AICoder, pid:0fec5t1bbcxbf3a14c100aa4e02bab8ac14008a2 */
    /* Started by AICoder, pid:n68a90757du9c3d1493c0a1de011c9778247e811 */
    private AssetStatisticsVo staticsAssetByMonth(String startTime, String endTime) {
        //获得当前月的月初和月末
        int firstDayOfMonth = DocAssetTimeUtils.getFirstDayOfMonth(Integer.parseInt(startTime));
        int lastDayOfMonth = DocAssetTimeUtils.getLastDayOfMonth(Integer.parseInt(endTime));
        AssetStatisticsVo assetStatisticsVo = new AssetStatisticsVo();
        //物料信息
        List<MaterialAssetMonthEntity> entities = materialAssetMapper.selectMonthDateByTimeRange(startTime, endTime);
        if(!entities.isEmpty()){
            assetStatisticsVo.setMaterialTotal(entities.get(0).getAllNum());
            assetStatisticsVo.setUpChangeNum(entities.get(0).getUpChangeNum());
            assetStatisticsVo.setDownChangeNum(entities.get(0).getDownChangeNum());
            long upNum = 0L;
            for (MaterialAssetMonthEntity data : entities) {
                upNum += data.getUpNum();
            }
            assetStatisticsVo.setUpNum(upNum);
            Long materialUpdateNum = materialAssetUpdMapper.countUpdatedByDay(String.valueOf(firstDayOfMonth), String.valueOf(lastDayOfMonth));
            assetStatisticsVo.setMaterialUpdateNum(materialUpdateNum);
        }
        //faq信息
        List<AssetFaqMonthPo> poList = assetFaqMonthMapper.selectDateByTimeRange(startTime, endTime);
        if(!poList.isEmpty()){
            // 计算数量信息
            Map<String, List<AssetFaqMonthPo>> dayMap = poList.stream()
                    .collect(Collectors.groupingBy(AssetFaqMonthPo::getDay));
            List<FaqStatisticVo.FaqStatDataVo> faqStatDataVos = dayMap.entrySet().stream()
                    .map(entry -> {
                        String day = entry.getKey();
                        List<AssetFaqMonthPo> dataList = entry.getValue();
                        long allNum = dataList.stream()
                                .mapToLong(data -> Long.parseLong(data.getAllNum())).sum();
                        long updateNum = entry.getValue().stream()
                                .mapToLong(data -> Long.parseLong(data.getUpdateNum())).sum();
                        long changeNum = entry.getValue().stream()
                                .mapToLong(data -> Long.parseLong(data.getChangeNum())).sum();
                        return new FaqStatisticVo.FaqStatDataVo(day, allNum, updateNum, changeNum);
                    })
                    .sorted((a, b) -> Long.compare(Long.parseLong(b.getDay()), Long.parseLong(a.getDay())))
                    .collect(Collectors.toList());
            assetStatisticsVo.setFaqTotal(faqStatDataVos.get(0).getAllNum());
            assetStatisticsVo.setFaqChangeNum(faqStatDataVos.get(0).getChangeNum());
            Long faqUpdateNum = assetFaqUpdMapper.countUpdatedByDay(String.valueOf(firstDayOfMonth), String.valueOf(lastDayOfMonth));
            assetStatisticsVo.setFaqUpdateNum(faqUpdateNum);
        }
        //文档信息
        List<DocAssetEntity> docAssetEntities = docAssetMonthMapper.selectByTimeRange(Integer.parseInt(startTime), Integer.parseInt(endTime));
        if(!docAssetEntities.isEmpty()){
            assetStatisticsVo.setDocTotal(docAssetEntities.get(0).getAllNum());
            assetStatisticsVo.setDocChangeNum(docAssetEntities.get(0).getChangeNum());
            Long docUpdateNum = docAssetMapper.countUpdatedByDay(firstDayOfMonth, lastDayOfMonth);
            assetStatisticsVo.setDocUpdateNum(docUpdateNum);
        }
        //商机信息
        /* Started by AICoder, pid:l8491af55fgf8b41402c0b0b7012db3608311004 */
        List<BusinessAssetMonthEntity> businessEntities = businessAssetMapper.selectMonthDateByTimeRange(startTime, endTime);
        if(!businessEntities.isEmpty()){
            long projectAddNum = 0L;
            long projectStartNum = 0L;
            for (BusinessAssetMonthEntity entity : businessEntities) {
                projectAddNum += entity.getProjectAddNum();
                projectStartNum += entity.getProjectStartNum();
            }
            assetStatisticsVo.setProjectAddNum(projectAddNum);
            assetStatisticsVo.setProjectStartNum(projectStartNum);
        }
        /* Ended by AICoder, pid:l8491af55fgf8b41402c0b0b7012db3608311004 */
        return assetStatisticsVo;
    }
    /* Ended by AICoder, pid:n68a90757du9c3d1493c0a1de011c9778247e811 */
    /* Started by AICoder, pid:c6ef6u95cc6000d146f909d31038977af8d49604 */
    private AssetStatisticsVo staticsAssetByYear(String startTime, String endTime) {
        //获得当前年的年初和年末
        int FirstDayOfYear = Integer.parseInt(LocalDate.of(Integer.parseInt(startTime), 1, 1).format(DateTimeFormatter.BASIC_ISO_DATE));
        int EndDayOfYear = Integer.parseInt(LocalDate.of(Integer.parseInt(endTime), 12, 31).format(DateTimeFormatter.BASIC_ISO_DATE));
        AssetStatisticsVo assetStatisticsVo = new AssetStatisticsVo();
        //物料信息
        List<MaterialAssetYearEntity> entities = materialAssetMapper.selectYearDateByTimeRange(startTime, endTime);
        if(!entities.isEmpty()){
            assetStatisticsVo.setMaterialTotal(entities.get(0).getAllNum());
            assetStatisticsVo.setUpChangeNum(entities.get(0).getUpChangeNum());
            assetStatisticsVo.setDownChangeNum(entities.get(0).getDownChangeNum());
            long upNum = 0L;
            for (MaterialAssetYearEntity data : entities) {
                upNum += data.getUpNum();
            }
            assetStatisticsVo.setUpNum(upNum);
            Long materialUpdateNum = materialAssetUpdMapper.countUpdatedByDay(String.valueOf(FirstDayOfYear), String.valueOf(EndDayOfYear));
            assetStatisticsVo.setMaterialUpdateNum(materialUpdateNum);
        }
        //faq信息
        List<AssetFaqYearPo> poList = assetFaqYearMapper.selectDateByTimeRange(startTime, endTime);
        if(!poList.isEmpty()){
            // 计算数量信息
            Map<String, List<AssetFaqYearPo>> dayMap = poList.stream()
                    .collect(Collectors.groupingBy(AssetFaqYearPo::getDay));
            List<FaqStatisticVo.FaqStatDataVo> faqStatDataVos = dayMap.entrySet().stream()
                    .map(entry -> {
                        String day = entry.getKey();
                        List<AssetFaqYearPo> dataList = entry.getValue();
                        long allNum = dataList.stream()
                                .mapToLong(data -> Long.parseLong(data.getAllNum())).sum();
                        long updateNum = entry.getValue().stream()
                                .mapToLong(data -> Long.parseLong(data.getUpdateNum())).sum();
                        long changeNum = entry.getValue().stream()
                                .mapToLong(data -> Long.parseLong(data.getChangeNum())).sum();
                        return new FaqStatisticVo.FaqStatDataVo(day, allNum, updateNum, changeNum);
                    })
                    .sorted((a, b) -> Long.compare(Long.parseLong(b.getDay()), Long.parseLong(a.getDay())))
                    .collect(Collectors.toList());
            assetStatisticsVo.setFaqTotal(faqStatDataVos.get(0).getAllNum());
            assetStatisticsVo.setFaqChangeNum(faqStatDataVos.get(0).getChangeNum());
            Long faqUpdateNum = assetFaqUpdMapper.countUpdatedByDay(String.valueOf(FirstDayOfYear), String.valueOf(EndDayOfYear));
            assetStatisticsVo.setFaqUpdateNum(faqUpdateNum);
        }
        //文档信息
        List<DocAssetEntity> docAssetEntities = docAssetYearMapper.selectByTimeRange(Integer.parseInt(startTime), Integer.parseInt(endTime));
        if(!docAssetEntities.isEmpty()){
            assetStatisticsVo.setDocTotal(docAssetEntities.get(0).getAllNum());
            assetStatisticsVo.setDocChangeNum(docAssetEntities.get(0).getChangeNum());
            Long docUpdateNum = docAssetMapper.countUpdatedByDay(FirstDayOfYear, EndDayOfYear);
            assetStatisticsVo.setDocUpdateNum(docUpdateNum);
        }
        //商机信息
        /* Started by AICoder, pid:f6962227e4g59fa142730ab050968b23cba9563b */
        List<BusinessAssetYearEntity> businessEntities = businessAssetMapper.selectYearDateByTimeRange(startTime, endTime);
        if(!businessEntities.isEmpty()){
            long projectAddNum = 0L;
            long projectStartNum = 0L;
            for (BusinessAssetYearEntity entity : businessEntities) {
                projectAddNum += entity.getProjectAddNum();
                projectStartNum += entity.getProjectStartNum();
            }
            assetStatisticsVo.setProjectAddNum(projectAddNum);
            assetStatisticsVo.setProjectStartNum(projectStartNum);
        }
        /* Ended by AICoder, pid:f6962227e4g59fa142730ab050968b23cba9563b */
        return assetStatisticsVo;
    }
    /* Ended by AICoder, pid:c6ef6u95cc6000d146f909d31038977af8d49604 */

    // 辅助方法：安全计算平均值
    /* Started by AICoder, pid:o24absab4f0561f1445e0bf350c192183bb2d99f */
    private static long calculateAverage(long total, long count) {
        if (count == 0) return 0L;

        // 处理整数溢出风险：当total超过Long.MAX_VALUE时自动降级为BigDecimal计算
        try {
            return Math.addExact(total / count, (total % count + count / 2) / count);
        } catch (ArithmeticException e) {
            return new BigDecimal(total)
                    .divide(new BigDecimal(count), 0, RoundingMode.HALF_UP)
                    .longValue();
        }
    }
    /* Ended by AICoder, pid:o24absab4f0561f1445e0bf350c192183bb2d99f */
    /* Ended by AICoder, pid:q33d1j4346bdd3e1453709d04081504994525149 */
}
