/* Started by AICoder, pid:1073dlb28afcd191431e0ad9b065762a0a155d8b */
package com.zte.uedm.dcdigital.domain.utils;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import lombok.extern.slf4j.Slf4j;

import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.temporal.WeekFields;

@Slf4j
public class ParseUtils {
    /**
     * 将字符串转换为整数
     *
     * @param str 待转换的字符串
     * @return 转换成功的整数值，空或null返回null
     * @throws BusinessException 当转换失败时抛出业务异常
     */
    public static Integer parseStringToInt(String str) {
        if (str == null || str.isEmpty()) return null;
        try {
            return Integer.valueOf(str);
        } catch (NumberFormatException e) {
            log.error("The conversion of string to integer failed,str:{}", str, e);
            throw new BusinessException(StatusCode.PARAM_ERROR);
        }
    }
    /**
     * 将整型日期转成"年-月-日"格式 (YYYYMMDD → YYYY-MM-DD)
     * @param date 整型日期值 (必须为8位正整数)
     * @return 格式化后的日期字符串
     * @throws BusinessException 当格式无效时抛出
     */
    public static String formatDay(int date) {
        if (date < 10000000 || date > 99999999) {
            log.error("Invalid date format: {}, must be 8 digits", date);
            throw new BusinessException(StatusCode.PARAM_ERROR);
        }

        int year = date / 10000;
        int month = (date % 10000) / 100;
        int day = date % 100;

        // 验证日期有效性
        if (month < 1 || month > 12 || day < 1 || day > 31) {
            log.error("Invalid date components: {}-{}-{}", year, month, day);
            throw new BusinessException(StatusCode.PARAM_ERROR);
        }

        return String.format("%d-%02d-%02d", year, month, day);
    }

    /**
     * 将整型周数转成"X年X周"格式 (YYYYWW → YYYY年W周)
     * @param week 整型周值 (必须为6位正整数)
     * @return 格式化后的周字符串
     * @throws BusinessException 当格式无效时抛出
     */
    public static String formatWeek(int week) {
        if (week < 100000 || week > 999999) {
            log.error("Invalid week format: {}, must be 6 digits", week);
            throw new BusinessException(StatusCode.PARAM_ERROR);
        }

        int year = week / 100;
        int weekNum = week % 100;

        // 验证周数有效性 (1-53)
        if (weekNum < 1 || weekNum > 53) {
            log.error("Invalid week number: {}", weekNum);
            throw new BusinessException(StatusCode.PARAM_ERROR);
        }

        return String.format("%d年%d周", year, weekNum);
    }

    /**
     * 将整型月份转成"年-月"格式 (YYYYMM → YYYY-MM)
     * @param month 整型月份值 (必须为6位正整数)
     * @return 格式化后的月份字符串
     * @throws BusinessException 当格式无效时抛出
     */
    public static String formatMonth(int month) {
        if (month < 100000 || month > 999999) {
            log.error("Invalid month format: {}, must be 6 digits", month);
            throw new BusinessException(StatusCode.PARAM_ERROR);
        }

        int year = month / 100;
        int mon = month % 100;

        // 验证月份有效性 (1-12)
        if (mon < 1 || mon > 12) {
            log.error("Invalid month number: {}", mon);
            throw new BusinessException(StatusCode.PARAM_ERROR);
        }

        return String.format("%d-%02d", year, mon);
    }

    /**
     * 将整型年份转成字符串 (YYYY → "YYYY")
     * @param year 整型年份值 (必须为4位正整数)
     * @return 年份字符串
     * @throws BusinessException 当格式无效时抛出
     */
    public static String formatYear(int year) {
        if (year < 1000 || year > 9999) {
            log.error("Invalid year format: {}, must be 4 digits", year);
            throw new BusinessException(StatusCode.PARAM_ERROR);
        }
        return String.valueOf(year);
    }

    /**
     * 将 YYYYMMDD 格式的整数转为 "yyyy年M月d日"
     * 示例：20250702 → "2025年7月2日"
     */
    public static String formatDayZn(int day) {
        if (String.valueOf(day).length() != 8) {
            log.error("Invalid day format: {}, must be 8 digits", day);
            throw new BusinessException(StatusCode.PARAM_ERROR);
        }
        int year = day / 10000;
        int month = (day / 100) % 100;
        int date = day % 100;
        return String.format("%d年%d月%d日", year, month, date);
    }

    /**
     * 将 YYYYWW 格式的整数转为 "yyyy年第w周"
     * 示例：202527 → "2025年第27周"
     */
    public static String formatWeekZn(int week) {
        if (String.valueOf(week).length() != 6) {
            log.error("Invalid week format: {}, must be 6 digits", week);
            throw new BusinessException(StatusCode.PARAM_ERROR);
        }
        int year = week / 100;
        int weekOfYear = week % 100;
        return String.format("%d年第%d周", year, weekOfYear);
    }

    /**
     * 将 YYYYMM 格式的整数转为 "yyyy年M月"
     * 示例：202507 → "2025年7月"
     */
    public static String formatMonthZn(int month) {
        if (String.valueOf(month).length() != 6) {
            log.error("Invalid month format: {}, must be 6 digits", month);
            throw new BusinessException(StatusCode.PARAM_ERROR);
        }
        int year = month / 100;
        int monthValue = month % 100;
        return String.format("%d年%d月", year, monthValue);
    }

    /**
     * 将 YYYY 格式的整数转为 "yyyy年"
     * 示例：2025 → "2025年"
     */
    public static String formatYearZn(int year) {
        if (String.valueOf(year).length() != 4) {
            log.error("Invalid year format: {}, must be 4 digits", year);
            throw new BusinessException(StatusCode.PARAM_ERROR);
        }
        return String.format("%d年", year);
    }

    public static int weekToDay(int yearAndWeek, boolean end) {
        int year = yearAndWeek / 100;
        int week = yearAndWeek % 100;
        WeekFields weekFields = WeekFields.ISO;
        LocalDate localDate = LocalDate.of(year, 1, 1)
                .with(weekFields.weekOfYear(), week)
                .with(weekFields.dayOfWeek(), 1)
                .plusDays(end ? 6 : 0);
        int month = localDate.getMonth().getValue();
        int day = localDate.getDayOfMonth();
        return year * 10000 + month * 100 + day;
    }

    public static int monthToDay(int yearAndMonth, boolean end) {
        int year = yearAndMonth / 100;
        int month = yearAndMonth % 100;
        YearMonth yearMonth = YearMonth.of(year, month);
        LocalDate localDate = yearMonth.atDay(1);
        if (end) {
            localDate = yearMonth.atEndOfMonth();
        }
        int day = localDate.getDayOfMonth();
        return year * 10000 + month * 100 + day;
    }

    public static int getBeginDay(int day, int timeType) {
        switch(timeType) {
            case 2:
                return weekToDay(day, false);
            case 3:
                return monthToDay(day, false);
            case 4:
                return day * 10000 + 100 + 1;
            default:
                return day;
        }
    }

    public static int getEndDay(int day, int timeType) {
        switch(timeType) {
            case 2:
                return weekToDay(day, true);
            case 3:
                return monthToDay(day, true);
            case 4:
                return day * 10000 + 1200 + 31;
            default:
                return day;
        }
    }

}

/* Ended by AICoder, pid:1073dlb28afcd191431e0ad9b065762a0a155d8b */