package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.DocAssetEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.DocAssetUpdEntity;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocAssetAddDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DocAssetMapper {
    DocAssetUpdEntity getDataByDayAndId(@Param("item") DocAssetAddDto item);

    int addRecord(DocAssetUpdEntity entity);

    List<String> getDataByDayAndProductCategoryId(@Param("beginDay") Integer beginDay, @Param("endDay") Integer endDay, @Param("productCategoryId") String productCategoryId);

    /**
     * 查询指定日期内faq的更新数
     */
    Long countUpdatedByDay(@Param("startDay") Integer startDay, @Param("endDay") Integer endDay);
}
