package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.MaterialAssetUpdEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 物料资产编辑记录表数据访问接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface MaterialAssetUpdMapper {

    /**
     * 插入物料编辑记录
     *
     * @param entity 物料编辑记录实体
     */
    void insertRecord(MaterialAssetUpdEntity entity);

    /**
     * 更新物料编辑记录
     *
     * @param entity 物料编辑记录实体
     */
    void updateRecord(MaterialAssetUpdEntity entity);

    /**
     * 根据物料ID和日期查询记录
     *
     * @param materialId 物料ID
     * @param day 日期
     * @return 物料编辑记录实体，不存在则返回null
     */
    MaterialAssetUpdEntity queryRecordByMaterialIdAndDay(@Param("materialId") String materialId,
                                                         @Param("day") String day);

    /**
     * 根据日期和产品小类ID统计更新的物料数量
     * 
     * @param day 日期
     * @param productCategoryId 产品小类ID
     * @return 更新的物料数量
     */
    Long countUpdatedMaterialsByDayAndCategory(@Param("day") String day, 
                                               @Param("productCategoryId") String productCategoryId);

    /**
     * 批量统计多个产品小类在指定日期的更新物料数量
     * 
     * @param day 日期
     * @param productCategoryIds 产品小类ID列表
     * @return 统计结果，key为产品小类ID，value为更新数量
     */
    List<Map<String, Object>> batchCountUpdatedMaterials(@Param("day") String day, 
                                                          @Param("productCategoryIds") List<String> productCategoryIds);

    /**
     * 查询指定日期范围内的编辑记录
     * 
     * @param startDay 开始日期
     * @param endDay 结束日期
     * @param productCategoryIds 产品小类ID列表
     * @return 编辑记录列表
     */
    List<MaterialAssetUpdEntity> queryRecordsByDateRange(@Param("startDay") String startDay,
                                                         @Param("endDay") String endDay,
                                                         @Param("productCategoryIds") List<String> productCategoryIds);
    /**
     * 查询指定日期内物料的更新数
     */
    Long countUpdatedByDay(@Param("startDay") String startDay, @Param("endDay") String endDay);
}
