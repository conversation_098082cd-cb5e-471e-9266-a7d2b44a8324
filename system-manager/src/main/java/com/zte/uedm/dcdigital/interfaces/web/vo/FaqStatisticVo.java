package com.zte.uedm.dcdigital.interfaces.web.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class FaqStatisticVo {

    /**
     * 数据列表
     */
    private List<FaqStatDataVo> dataList = new ArrayList<>();

    /**
     * 下级列表
     */
    private List<FaqStatJuniorVo> juniorList = new ArrayList<>();

    /**
     * 下级统计文档总数
     */
    private long allNum;

    /**
     * 下级统计变化数
     */
    private long changeNum;

    /**
     * 下级统计更新数
     */
    private long updateNum;

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FaqStatDataVo {

        /**
         * 日期
         */
        private String day;

        /**
         * 总数
         */
        private long allNum;

        /**
         * 更新数
         */
        private long updateNum;

        /**
         * 变化数
         */
        private long changeNum;

    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FaqStatJuniorVo {

        /**
         * 产品小类
         */
        @ExcelProperty(value = "产品小类", index = 0)
        private String productCategoryName;

        /**
         * 总数
         */
        @ExcelProperty(value = "总数", index = 1)
        private long allNum;

        /**
         * 更新数
         */
        @ExcelProperty(value = "更新数", index = 2)
        private long updateNum;

        /**
         * 变化数
         */
        @ExcelProperty(value = "变化数", index = 3)
        private long changeNum;

        @ExcelProperty(value = "时间", index = 4)
        private String day;
    }
}
