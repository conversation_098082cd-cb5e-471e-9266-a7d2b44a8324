package com.zte.uedm.dcdigital.domain.utils;

import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.List;

/**
 * 物料资产时间处理工具类
 * 
 * <AUTHOR>
 */
@Slf4j
public class MaterialAssetTimeUtils {

    private static final DateTimeFormatter DAY_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyyyMM");


    /**
     * 生成时间范围内的所有时间节点
     *
     * @param startTime 开始时间，格式根据timeType而定：
     *                  - timeType=1(天): yyyyMMdd (如: 20250105)
     *                  - timeType=2(周): yyyyww (如: 202530)
     *                  - timeType=3(月): yyyyMM (如: 202505)
     *                  - timeType=4(年): yyyy (如: 2025)
     * @param endTime 结束时间，格式同startTime
     * @param timeType 时间类型：1-天，2-周，3-月，4-年
     * @return 时间节点列表，输出格式与输入格式相同
     */
    public static List<String> generateTimeNodes(String startTime, String endTime, Integer timeType) {
        List<String> timeNodes = new ArrayList<>();

        try {
            switch (timeType) {
                case 1: // 天 - 输入输出格式：yyyyMMdd
                    timeNodes = generateDayNodes(startTime, endTime);
                    break;
                case 2: // 周 - 输入输出格式：yyyyww
                    timeNodes = generateWeekNodes(startTime, endTime);
                    break;
                case 3: // 月 - 输入输出格式：yyyyMM
                    timeNodes = generateMonthNodes(startTime, endTime);
                    break;
                case 4: // 年 - 输入输出格式：yyyy
                    timeNodes = generateYearNodes(startTime, endTime);
                    break;
                default:
                    log.warn("Unsupported time type for generating time nodes: {}", timeType);
            }
        } catch (Exception e) {
            log.error("Error generating time nodes with startTime: {}, endTime: {}, timeType: {}", startTime, endTime, timeType, e);
        }

        return timeNodes;
    }


    /**
     * 生成日期节点（输入格式：yyyyMMdd，输出格式：yyyyMMdd）
     */
    private static List<String> generateDayNodes(String startTime, String endTime) {
        List<String> dayNodes = new ArrayList<>();

        try {
            // 验证和格式化输入时间（天格式：yyyyMMdd）
            String formattedStartTime = validateAndFormatTimeInput(startTime, 1);
            String formattedEndTime = validateAndFormatTimeInput(endTime, 1);

            log.debug("Generating day nodes from {} to {}", formattedStartTime, formattedEndTime);

            LocalDate start = LocalDate.parse(formattedStartTime, DateTimeFormatter.ofPattern("yyyyMMdd"));
            LocalDate end = LocalDate.parse(formattedEndTime, DateTimeFormatter.ofPattern("yyyyMMdd"));

            LocalDate current = start;
            while (!current.isAfter(end)) {
                dayNodes.add(current.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                current = current.plusDays(1);
            }

            log.debug("Generated {} day nodes", dayNodes.size());
        } catch (Exception e) {
            log.error("Error generating day nodes with startTime: {}, endTime: {}", startTime, endTime, e);
        }

        return dayNodes;
    }

    /**
     * 验证和格式化时间输入（根据timeType使用不同格式）
     * @param timeInput 输入的时间字符串
     * @param timeType 时间类型：1-天，2-周，3-月，4-年
     * @return 格式化后的时间字符串
     */
    private static String validateAndFormatTimeInput(String timeInput, Integer timeType) {
        if (timeInput == null || timeInput.trim().isEmpty()) {
            throw new IllegalArgumentException("Time input cannot be null or empty");
        }

        String trimmedInput = timeInput.trim();
        // 移除所有非数字字符
        String numericInput = trimmedInput.replaceAll("[^0-9]", "");

        switch (timeType) {
            case 1: // 天 - 期望格式：yyyyMMdd (8位)
                return validateDayFormat(numericInput, timeInput);
            case 2: // 周 - 期望格式：yyyyww (6位)
                return validateWeekFormat(numericInput, timeInput);
            case 3: // 月 - 期望格式：yyyyMM (6位)
                return validateMonthFormat(numericInput, timeInput);
            case 4: // 年 - 期望格式：yyyy (4位)
                return validateYearFormat(numericInput, timeInput);
            default:
                throw new IllegalArgumentException("Unsupported time type: " + timeType);
        }
    }

    /**
     * 验证天格式：yyyyMMdd
     */
    private static String validateDayFormat(String numericInput, String originalInput) {
        if (numericInput.length() == 8) {
            try {
                LocalDate.parse(numericInput, DateTimeFormatter.ofPattern("yyyyMMdd"));
                return numericInput;
            } catch (Exception e) {
                throw new IllegalArgumentException("Invalid day format: " + originalInput + ", expected yyyyMMdd");
            }
        } else {
            throw new IllegalArgumentException("Invalid day format: " + originalInput + ", expected yyyyMMdd (8 digits), got " + numericInput.length() + " digits");
        }
    }

    /**
     * 验证周格式：yyyyww
     */
    private static String validateWeekFormat(String numericInput, String originalInput) {
        if (numericInput.length() == 6) {
            try {
                int year = Integer.parseInt(numericInput.substring(0, 4));
                int week = Integer.parseInt(numericInput.substring(4, 6));
                if (year < 1900 || year > 2100) {
                    throw new IllegalArgumentException("Year out of valid range: " + year);
                }
                if (week < 1 || week > 53) {
                    throw new IllegalArgumentException("Week out of valid range: " + week);
                }
                return numericInput;
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("Invalid week format: " + originalInput + ", expected yyyyww");
            }
        } else {
            throw new IllegalArgumentException("Invalid week format: " + originalInput + ", expected yyyyww (6 digits), got " + numericInput.length() + " digits");
        }
    }

    /**
     * 验证月格式：yyyyMM
     */
    private static String validateMonthFormat(String numericInput, String originalInput) {
        if (numericInput.length() == 6) {
            try {
                LocalDate.parse(numericInput + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
                return numericInput;
            } catch (Exception e) {
                throw new IllegalArgumentException("Invalid month format: " + originalInput + ", expected yyyyMM");
            }
        } else {
            throw new IllegalArgumentException("Invalid month format: " + originalInput + ", expected yyyyMM (6 digits), got " + numericInput.length() + " digits");
        }
    }

    /**
     * 验证年格式：yyyy
     */
    private static String validateYearFormat(String numericInput, String originalInput) {
        if (numericInput.length() == 4) {
            try {
                int year = Integer.parseInt(numericInput);
                if (year < 1900 || year > 2100) {
                    throw new IllegalArgumentException("Year out of valid range: " + year);
                }
                return numericInput;
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("Invalid year format: " + originalInput + ", expected yyyy");
            }
        } else {
            throw new IllegalArgumentException("Invalid year format: " + originalInput + ", expected yyyy (4 digits), got " + numericInput.length() + " digits");
        }
    }

    /**
     * 生成周节点（输入格式：yyyyww，输出格式：yyyyww）
     */
    private static List<String> generateWeekNodes(String startTime, String endTime) {
        List<String> weekNodes = new ArrayList<>();

        try {
            // 验证和格式化输入时间（周格式：yyyyww）
            String formattedStartTime = validateAndFormatTimeInput(startTime, 2);
            String formattedEndTime = validateAndFormatTimeInput(endTime, 2);

            log.debug("Generating week nodes from {} to {}", formattedStartTime, formattedEndTime);

            // 解析周格式：yyyyww
            int startYear = Integer.parseInt(formattedStartTime.substring(0, 4));
            int startWeek = Integer.parseInt(formattedStartTime.substring(4, 6));
            int endYear = Integer.parseInt(formattedEndTime.substring(0, 4));
            int endWeek = Integer.parseInt(formattedEndTime.substring(4, 6));

            // 生成周范围内的所有周节点
            int currentYear = startYear;
            int currentWeek = startWeek;

            while (currentYear < endYear || (currentYear == endYear && currentWeek <= endWeek)) {
                String weekStr = String.format("%04d%02d", currentYear, currentWeek);
                weekNodes.add(weekStr);

                currentWeek++;
                // 检查是否需要进入下一年
                if (currentWeek > 52) { // 简化处理，大部分年份有52周
                    currentYear++;
                    currentWeek = 1;
                }
            }

            log.debug("Generated {} week nodes", weekNodes.size());
        } catch (Exception e) {
            log.error("Error generating week nodes with startTime: {}, endTime: {}", startTime, endTime, e);
        }

        return weekNodes;
    }

    /**
     * 生成月节点（输入格式：yyyyMM，输出格式：yyyyMM）
     */
    private static List<String> generateMonthNodes(String startTime, String endTime) {
        List<String> monthNodes = new ArrayList<>();

        try {
            // 验证和格式化输入时间（月格式：yyyyMM）
            String formattedStartTime = validateAndFormatTimeInput(startTime, 3);
            String formattedEndTime = validateAndFormatTimeInput(endTime, 3);

            log.debug("Generating month nodes from {} to {}", formattedStartTime, formattedEndTime);

            // 解析月格式：yyyyMM
            LocalDate start = LocalDate.parse(formattedStartTime + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
            LocalDate end = LocalDate.parse(formattedEndTime + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));

            // 从开始月份开始生成
            LocalDate current = start;

            while (!current.isAfter(end)) {
                monthNodes.add(current.format(DateTimeFormatter.ofPattern("yyyyMM")));
                current = current.plusMonths(1);
            }

            log.debug("Generated {} month nodes", monthNodes.size());
        } catch (Exception e) {
            log.error("Error generating month nodes with startTime: {}, endTime: {}", startTime, endTime, e);
        }

        return monthNodes;
    }

    /**
     * 生成年节点（输入格式：yyyy，输出格式：yyyy）
     */
    private static List<String> generateYearNodes(String startTime, String endTime) {
        List<String> yearNodes = new ArrayList<>();

        try {
            // 验证和格式化输入时间（年格式：yyyy）
            String formattedStartTime = validateAndFormatTimeInput(startTime, 4);
            String formattedEndTime = validateAndFormatTimeInput(endTime, 4);

            log.debug("Generating year nodes from {} to {}", formattedStartTime, formattedEndTime);

            // 解析年格式：yyyy
            int startYear = Integer.parseInt(formattedStartTime);
            int endYear = Integer.parseInt(formattedEndTime);

            // 生成年范围内的所有年节点
            for (int year = startYear; year <= endYear; year++) {
                yearNodes.add(String.valueOf(year));
            }

            log.debug("Generated {} year nodes", yearNodes.size());
        } catch (Exception e) {
            log.error("Error generating year nodes with startTime: {}, endTime: {}", startTime, endTime, e);
        }

        return yearNodes;
    }


    /**
     * 获取前一个时间点
     * 
     * @param currentTimePoint 当前时间点
     * @param timeType 时间类型
     * @return 前一个时间点
     */
    public static String getPreviousTimePoint(String currentTimePoint, Integer timeType) {
        try {
            switch (timeType) {
                case 1: // 天
                    LocalDate currentDay = LocalDate.parse(currentTimePoint, DAY_FORMATTER);
                    return currentDay.minusDays(1).format(DAY_FORMATTER);
                case 2: // 周
                    // 周格式为YYYYWW，需要特殊处理，使用ISO周字段
                    int year = Integer.parseInt(currentTimePoint.substring(0, 4));
                    int week = Integer.parseInt(currentTimePoint.substring(4, 6));
                    if (week > 1) {
                        return String.format("%04d%02d", year, week - 1);
                    } else {
                        // 获取上一年的最后一周，使用ISO周字段计算
                        LocalDate lastDayOfPreviousYear = LocalDate.of(year - 1, 12, 31);
                        int lastWeekOfPreviousYear = lastDayOfPreviousYear.get(WeekFields.ISO.weekOfYear());
                        return String.format("%04d%02d", year - 1, lastWeekOfPreviousYear);
                    }
                case 3: // 月
                    LocalDate currentMonth = LocalDate.parse(currentTimePoint + "01", DAY_FORMATTER);
                    return currentMonth.minusMonths(1).format(MONTH_FORMATTER);
                case 4: // 年
                    int currentYear = Integer.parseInt(currentTimePoint);
                    return String.valueOf(currentYear - 1);
                default:
                    log.error("Invalid time type: {}", timeType);
                    return currentTimePoint;
            }
        } catch (Exception e) {
            log.error("Error getting previous time point, currentTimePoint: {}, timeType: {}", 
                     currentTimePoint, timeType, e);
            return currentTimePoint;
        }
    }

}
