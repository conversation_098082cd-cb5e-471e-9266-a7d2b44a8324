<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.MaterialAssetUpdMapper">

    <!-- 物料编辑记录表结果映射 -->
    <resultMap id="MaterialAssetUpdResultMap" type="com.zte.uedm.dcdigital.domain.aggregate.model.entity.MaterialAssetUpdEntity">
        <id column="id" property="id"/>
        <result column="day" property="day"/>
        <result column="product_category_id" property="productCategoryId"/>
        <result column="material_id" property="materialId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 插入物料编辑记录 -->
    <insert id="insertRecord" parameterType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.MaterialAssetUpdEntity">
        INSERT INTO asset_material_upd (
            id,
            day,
            product_category_id,
            material_id,
            create_time
        ) VALUES (
            #{id},
            #{day},
            #{productCategoryId},
            #{materialId},
            #{createTime}
        )
    </insert>

    <!-- 更新物料编辑记录 -->
    <update id="updateRecord" parameterType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.MaterialAssetUpdEntity">
        UPDATE asset_material_upd
        SET
            product_category_id = #{productCategoryId},
            create_time = #{createTime}
        WHERE id = #{id}
    </update>

    <!-- 根据物料ID和日期查询记录 -->
    <select id="queryRecordByMaterialIdAndDay" resultMap="MaterialAssetUpdResultMap">
        SELECT
            id,
            day,
            product_category_id,
            material_id,
            create_time
        FROM asset_material_upd
        WHERE material_id = #{materialId}
        AND day = #{day}
        LIMIT 1
    </select>

    <!-- 根据日期和产品小类ID统计更新的物料数量 -->
    <select id="countUpdatedMaterialsByDayAndCategory" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT material_id)
        FROM asset_material_upd
        WHERE day = #{day}
        AND product_category_id = #{productCategoryId}
    </select>

    <!-- 批量统计多个产品小类在指定日期的更新物料数量 -->
    <select id="batchCountUpdatedMaterials" resultType="java.util.Map">
        SELECT 
            product_category_id as productCategoryId,
            COUNT(DISTINCT material_id) as updateCount
        FROM asset_material_upd
        WHERE day = #{day}
        AND product_category_id IN
        <foreach collection="productCategoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
        GROUP BY product_category_id
    </select>

    <!-- 查询指定日期范围内的编辑记录 -->
    <select id="queryRecordsByDateRange" resultMap="MaterialAssetUpdResultMap">
        SELECT 
            id,
            day,
            product_category_id,
            material_id,
            create_time
        FROM asset_material_upd
        WHERE day BETWEEN #{startDay} AND #{endDay}
        <if test="productCategoryIds != null and productCategoryIds.size() > 0">
            AND product_category_id IN
            <foreach collection="productCategoryIds" item="categoryId" open="(" separator="," close=")">
                #{categoryId}
            </foreach>
        </if>
        ORDER BY day DESC, create_time DESC
    </select>

    <select id="countUpdatedByDay" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT material_id)
        FROM asset_material_upd
        WHERE day BETWEEN #{startDay} AND #{endDay}
    </select>

</mapper>
