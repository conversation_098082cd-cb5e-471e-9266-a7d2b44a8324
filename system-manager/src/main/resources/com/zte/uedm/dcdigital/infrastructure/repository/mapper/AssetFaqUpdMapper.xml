<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.AssetFaqUpdMapper">


    <insert id="insert">
        INSERT INTO asset_faq_upd
            (id, day, product_category_id, faq_id, create_time)
        VALUES
            (#{id}, #{day}, #{productCategoryId}, #{faqId}, #{createTime})
    </insert>
    <select id="selectUpdateNum" resultType="java.lang.Integer">
        select count(*) from asset_faq_upd where day = #{todayStr} and faq_id = #{faqId}
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        SELECT count(distinct faq_id)  FROM asset_faq_upd where day = #{dayStr}
        and product_category_id = #{productCategoryId}
    </select>

    <select id="countUpdatedByDay" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT faq_id)
        FROM asset_faq_upd
        WHERE day BETWEEN #{startDay} AND #{endDay}
    </select>
</mapper>