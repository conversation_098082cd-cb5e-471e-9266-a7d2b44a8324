<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.DocAssetMapper">

    <select id="getDataByDayAndId"
            resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.DocAssetUpdEntity">
        SELECT id, day, product_category_id, document_id, create_time
        FROM asset_document_upd
        where day = #{item.day}
        and document_id = #{item.documentId}
    </select>

    <insert id="addRecord" parameterType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.DocAssetUpdEntity">
        INSERT INTO asset_document_upd (id, day, product_category_id, document_id, create_time)
        VALUES (#{id}, #{day}, #{productCategoryId}, #{documentId}, #{createTime})
    </insert>

    <select id="getDataByDayAndProductCategoryId" resultType="string">
        SELECT document_id FROM asset_document_upd
        where day BETWEEN #{beginDay} AND #{endDay}
        <if test="productCategoryId != null">
            and product_category_id = #{productCategoryId}
        </if>
        <if test="productCategoryId == null">
            and product_category_id IS NULL
        </if>
        GROUP BY document_id
    </select>

    <select id="countUpdatedByDay" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT document_id)
        FROM asset_document_upd
        WHERE day BETWEEN #{startDay} AND #{endDay}
    </select>

</mapper>