package com.zte.uedm.dcdigital.application.deependesign.executor.impl;

import com.zte.uedm.dcdigital.application.deependesign.executor.DeepenDesignQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.domain.common.valueobj.PageVoUpgrade;
import com.zte.uedm.dcdigital.domain.service.DeepenDesignDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.DeepenImplementationHistoryQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProductUpgradeQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 深化设计查询服务实现类
 */
@Service
@Slf4j
public class DeepenDesignQueryServiceImpl implements DeepenDesignQueryService {

    @Autowired
    private DeepenDesignDomainService deepenDesignDomainService;

    @Override
    public DeepenImplementationVo queryDeepenImplementation(String itemId) {
        log.info("DeepenDesignQueryService.queryDeepenImplementation start, itemId: {}", itemId);
        return deepenDesignDomainService.queryDeepenImplementation(itemId);
    }

    @Override
    public PageVO<DeepenImplementationHistoryVo> queryImplementationHistory(DeepenImplementationHistoryQueryDto queryDto) {
        log.info("DeepenDesignQueryService.queryImplementationHistory start, queryDto: {}", queryDto);
        return deepenDesignDomainService.queryImplementationHistory(queryDto);
    }

    @Override
    public List<ConstructionDrawingReviewVo> queryAllRecord(String itemId) {
        log.info("DeepenDesignQueryService.queryAllRecord start, itemId: {}", itemId);
        return deepenDesignDomainService.queryAllRecord(itemId);
    }

    @Override
    public ConstructionDrawingReviewVo queryByRecordId(String id) {
        log.info("DeepenDesignQueryService.queryByRecordId start, id: {}", id);
        return deepenDesignDomainService.queryByRecordId(id);
    }

    @Override
    public PageVO<ProductCategorySimpleVo> queryCategoryByItemId(String itemId, Integer pageNo, Integer pageSize) {
        log.info("DeepenDesignQueryService.queryCategoryByItemId start, itemId: {}", itemId);
        return deepenDesignDomainService.queryCategoryByItemId(itemId, pageNo, pageSize);
    }

    @Override
    public DeepenDesignOverviewVo queryOverview(String id) {
        log.info("DeepenDesignQueryService.queryOverview start, id: {}", id);
        return deepenDesignDomainService.queryOverview(id);
    }

    @Override
    public DeepenImplementationVo innerQueryDeepenImplementation(String itemId) {
        return deepenDesignDomainService.innerQueryDeepenImplementation(itemId);
    }

    @Override
    public PageVoUpgrade<ProductUpgradeVo> querySubmissionInfoByItemId(ProductUpgradeQueryDto queryDto) {
        return deepenDesignDomainService.querySubmissionInfoByItemId(queryDto);
    }

    @Override
    public List<DeepenImplementationVo> queryInnerDeepenImplementationList(List<String> itemIds) {
        return deepenDesignDomainService.queryInnerDeepenImplementationList(itemIds);
    }
}
