package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProjectDeepenImplementationPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目深化实施表数据库访问层接口
 */
@Mapper
public interface ProjectDeepenImplementationMapper extends BaseMapper<ProjectDeepenImplementationPo> {

    /**
     * 根据项目ID查询深化实施信息
     *
     * @param itemId 项目ID
     * @return 深化实施信息
     */
    ProjectDeepenImplementationPo selectByItemId(@Param("itemId") String itemId);

    /**
     * 插入深化实施记录
     *
     * @param po 深化实施记录
     * @return 影响的行数
     */
    int insertDeepenImplementation(ProjectDeepenImplementationPo po);

    /**
     * 根据ID更新深化实施记录
     *
     * @param po 包含更新数据及ID的实体对象
     * @return 影响的行数
     */
    int updateDeepenImplementationById(ProjectDeepenImplementationPo po);

    List<ProjectDeepenImplementationPo> selectDeepenImplementationListByItemIds(List<String> itemIds);
}
