package com.zte.uedm.dcdigital.application.deependesign.executor;

import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.domain.common.valueobj.PageVoUpgrade;
import com.zte.uedm.dcdigital.interfaces.web.dto.DeepenImplementationHistoryQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProductUpgradeQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.*;

import java.util.List;

/**
 * 深化设计查询服务接口
 */
public interface DeepenDesignQueryService {

    /**
     * 查询深化实施（包含成果输出）信息
     *
     * @param itemId 项目ID
     * @return 深化实施信息
     */
    DeepenImplementationVo queryDeepenImplementation(String itemId);

    /**
     * 查询深化实施历史记录（按更新时间倒序）
     *
     * @param queryDto 查询条件
     * @return 深化实施历史记录分页列表
     */
    PageVO<DeepenImplementationHistoryVo> queryImplementationHistory(DeepenImplementationHistoryQueryDto queryDto);

    /**
     * 查询项目所有施工图会审记录
     *
     * @param itemId 项目ID
     * @return 会审记录列表
     */
    List<ConstructionDrawingReviewVo> queryAllRecord(String itemId);

    /**
     * 根据记录ID查询施工图会审记录详情
     *
     * @param id 会审记录ID
     * @return 会审记录详情
     */
    ConstructionDrawingReviewVo queryByRecordId(String id);

    /**
     * 根据项目ID查询产品小类
     *
     * @param itemId 项目ID
     * @return 产品小类信息列表（包含ID和pathname）
     */
    PageVO<ProductCategorySimpleVo> queryCategoryByItemId(String itemId, Integer pageNo, Integer pageSize);

    /**
     * 项目概览-工程深化设计信息-信息查询
     *
     * @param id 项目ID
     * @return 工程深化设计概览信息
     */
    DeepenDesignOverviewVo queryOverview(String id);


    /**
     * 内部调用接口查询深化实施（包含成果输出）信息
     *
     * @param itemId 项目ID
     * @return 深化实施信息
     */
    DeepenImplementationVo innerQueryDeepenImplementation(String itemId);


    /**
     * 项目概览-工程深化设计信息-提资信息查询
     * @param queryDto 项目ID
     * */
    PageVoUpgrade<ProductUpgradeVo> querySubmissionInfoByItemId(ProductUpgradeQueryDto queryDto);

    List<DeepenImplementationVo> queryInnerDeepenImplementationList(List<String> itemIds);
}
