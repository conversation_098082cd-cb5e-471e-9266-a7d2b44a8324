package com.zte.uedm.dcdigital.interfaces.inner;


/* Started by AICoder, pid:5233chbe823cc1e14b4008e1803e294ed490c784 */
import com.zte.uedm.dcdigital.application.project.*;
import com.zte.uedm.dcdigital.application.statistics.BusinessStatisticsService;
import com.zte.uedm.dcdigital.common.bean.project.ProjectDetailInfoVo;
import com.zte.uedm.dcdigital.application.projectArea.executor.BrandGuideCommandService;
import com.zte.uedm.dcdigital.application.projectArea.executor.BrandGuideQueryService;
import com.zte.uedm.dcdigital.common.bean.project.*;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.web.dto.BusinessStatisticsQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.LaunchBidDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BusinessStatisticsVo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.Map;

/**
 * ProjectInnerController 类用于处理与项目相关的内部查询请求。
 */
@Path("/project-inner")
@Controller
@Slf4j
public class ProjectInnerController {

    /**
     * 项目查询服务，用于调用项目相关的查询方法。
     */
    @Autowired
    private ProjectQueryService projectQueryService;

    @Autowired
    private ProjectCommandService projectCommandService;
    @Autowired
    private BrandGuideQueryService brandGuideQueryService;
    @Autowired
    private BrandGuideCommandService brandGuideCommandService;
    @Autowired
    private OpportunitySupportCommandService supportCommandService;
    @Autowired
    private OpportunitySupportQueryService supportQueryService;

    @Autowired
    private BillOfQuantityQueryService billOfQuantityQueryService;
    @Autowired
    private BusinessStatisticsService businessStatisticsService;

    /**
     * 根据项目ID获取项目的详细信息。
     *
     * @param id 项目ID，用于查询具体的项目详情。
     * @return 包含查询结果的BaseResult对象。如果未找到项目，返回的结果将包含空的ProjectDetailInfoVo对象。
     */
    @GET
    @Path("/detail-by-id")
    @Produces({MediaType.APPLICATION_JSON})
    public BaseResult<ProjectDetailInfoVo> getProjectDetail(@QueryParam("id") String id) {
        return BaseResult.success(projectQueryService.queryProjectDetail(id));
    }

    /**
     * 根据项目ID更新项目投标信息。
     *
     * @param launchBidDto 项目投标信息。
     */
    @POST
    @Path("/update-launch-bid-by-id")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "更新项目投标信息", notes = "更新项目投标信息", httpMethod = "POST")
    public BaseResult<Object> updateLaunchBidById(LaunchBidDto launchBidDto) {
        log.info("Update the task completion time launchBidDto: {}", launchBidDto);
        projectCommandService.updateLaunchBidById(launchBidDto);
        return BaseResult.success();
    }
    /**
     * 根据商机ID查询商机投标信息。
     *
     * @param projectId 项目id。
     */
    @POST
    @Path("/query-launch-bid-by-id")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "根据商机ID查询商机投标信息", notes = "根据商机ID查询商机投标信息", httpMethod = "POST")
    public BaseResult<Object> queryLaunchBidById(@QueryParam("projectId") String projectId) {
        LaunchBiddingInnerVo launchBiddingInnerVo=projectCommandService.queryLaunchBidById(projectId);
        return BaseResult.success(launchBiddingInnerVo);
    }

    /**
     * 根据商机ID查询商机投标信息。
     *
     * @param projectIds 商机ID列表。
     */
    @POST
    @Path("/query-launch-bid-list-by-ids")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "根据商机ID列表查询项目投标信息", notes = "根据商机ID查询商机投标信息", httpMethod = "POST")
    public BaseResult<Object> queryLaunchBidListByIds(@QueryParam("projectIds") List<String> projectIds) {
        List<LaunchBiddingInnerVo> launchBiddingInnerVos=projectCommandService.queryLaunchBidListByIds(projectIds);
        return BaseResult.success(launchBiddingInnerVos);
    }

    /**
     * 根据地区ID获取项目id集。
     *
     * @param id 地区ID，用于查询具体的项目详情。
     * @return 包含查询结果的BaseResult对象。
     */
    @GET
    @Path("/query-by-areaId")
    @Produces({MediaType.APPLICATION_JSON})
    public BaseResult<List<String>> queryProjectIdByAreaId(@QueryParam("id") String id) {
        List<String> projectIds = projectQueryService.queryProjectIdByAreaId(id);
        return BaseResult.success(projectIds);
    }

    @GET
    @Path("/query-guide-subcategory")
    @Produces({MediaType.APPLICATION_JSON})
    public BaseResult<List<BrandGuidanceVo>> queryGuidedSubcategory(@QueryParam("projectId") String projectId) {
        List<BrandGuidanceVo> subcategoryIds = projectQueryService.queryGuidedSubcategory(projectId);
        return BaseResult.success(subcategoryIds);
    }
    @POST
    @Path("/update-short-brand")
    @Produces({MediaType.APPLICATION_JSON})
    public BaseResult<Void> updateGuidedShortBrand(ShortBrandUpdateDto shortBrandUpdateDto) {
        brandGuideCommandService.updateGuidedShortBrand(shortBrandUpdateDto);
        return BaseResult.success();
    }
    /* Started by AICoder, pid:y4adaq335csdfdb146e70a93f001a33106a260b0 */
    /**
     * 添加商机-产品小类-市场任务产品支持SE
     */
    @POST
    @Path("/add-opportunity-support")
    @Produces({MediaType.APPLICATION_JSON})
    public BaseResult<Void> addOpportunitySupport(OpportunitySupportInnerDto supportInnerDto) {
        log.info("addOpportunitySupport supportInnerDto: {}", supportInnerDto);
        supportCommandService.addOpportunitySupport(supportInnerDto);
        return BaseResult.success();
    }

    /**
     * 更新商机-产品小类-市场任务产品支持SE
     */
    @POST
    @Path("/update-opportunity-support")
    @Produces({MediaType.APPLICATION_JSON})
    public BaseResult<Void> updateOpportunitySupport(OpportunitySupportInnerDto supportInnerDto) {
        log.info("updateOpportunitySupport supportInnerDto: {}", supportInnerDto);
        supportCommandService.updateOpportunitySupport(supportInnerDto);
        return BaseResult.success();
    }

    /**
     * 查询商机-产品小类-市场任务产品支持SE
     */
    @POST
    @Path("/get-opportunity-support")
    @Produces({MediaType.APPLICATION_JSON})
    public BaseResult<OpportunitySupportInnerVo> getOpportunitySupport(OpportunitySupportInnerDto supportInnerDto) {
        log.info("getOpportunitySupport supportInnerDto: {}", supportInnerDto);
        OpportunitySupportInnerVo supportInnerVo = supportQueryService.getOpportunitySupport(supportInnerDto);
        log.info("getOpportunitySupport supportInnerVo: {}", supportInnerVo);
        return BaseResult.success(supportInnerVo);
    }

    /* Ended by AICoder, pid:y4adaq335csdfdb146e70a93f001a33106a260b0 */

    @POST
    @Path("/query-project-product-support")
    @Produces({MediaType.APPLICATION_JSON})
    public BaseResult<List<ProjectProductSupportsVo>> queryProjectProductSupport(@QueryParam("projectId") String projectId,@QueryParam("productCategoryIds") List<String> productCategoryIds) {
        List<ProjectProductSupportsVo> supportsVoList = projectQueryService.queryProjectProductSupport(projectId,productCategoryIds);
        return BaseResult.success(supportsVoList);
    }

    @POST
    @Path("/query-bill-of-quantities")
    @Produces({MediaType.APPLICATION_JSON})
    public BaseResult<BillOfQuantityInnerVo> queryBillOfQuantitiesById(@QueryParam("id") String id) {
        BillOfQuantityInnerVo result = billOfQuantityQueryService.queryBillOfQuantitiesById(id);
        return BaseResult.success(result);
    }

    @POST
    @Path("/area-business-data")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "获取所有地区下级商机统计数据", notes = "传入时间节点或时间范围，查询所有地区的商机统计数据", httpMethod = "POST")
    public BaseResult<List<BusinessStatisticsVo>> getAreaBusinessStatistics(BusinessStatisticsQueryDto queryDto) {
        log.info("Received business statistics query request: {}", queryDto);

        try {

            // 调用服务获取统计数据
            List<BusinessStatisticsVo> result = businessStatisticsService.getAreaBusinessStatistics(queryDto);

            log.info("Successfully generated {} business statistics records", result.size());
            return BaseResult.success(result);

        } catch (IllegalArgumentException e) {
            log.warn("Invalid request parameters: {}", e.getMessage());
            return BaseResult.failed( "Invalid parameters: " + e.getMessage());

        } catch (Exception e) {
            log.error("Error occurred while processing business statistics request", e);
            return BaseResult.failed("Internal server error: " + e.getMessage());
        }
    }

    /**
     * 获取所有地区信息映射
     *
     * @return Map<地区ID, 地区名称>
     */
    @GET
    @Path("/all-areas")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "获取所有地区信息", notes = "返回所有地区的ID和名称映射", httpMethod = "GET")
    public BaseResult<Map<String, String>> getAllAreas() {
        log.info("Received request to get all areas");

        try {
            Map<String, String> areaMap = businessStatisticsService.getAllAreaMap();

            log.info("Successfully retrieved {} areas", areaMap.size());
            return BaseResult.success(areaMap);

        } catch (Exception e) {
            log.error("Error occurred while getting all areas", e);
            return BaseResult.failed("Internal server error: " + e.getMessage());
        }
    }
}
/* Ended by AICoder, pid:5233chbe823cc1e14b4008e1803e294ed490c784 */
