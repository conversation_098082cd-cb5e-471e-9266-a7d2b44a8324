package com.zte.uedm.dcdigital.interfaces.web.controller;

import com.zte.uedm.dcdigital.application.deependesign.executor.DeepenDesignCommandService;
import com.zte.uedm.dcdigital.application.deependesign.executor.DeepenDesignQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.domain.common.valueobj.PageVoUpgrade;
import com.zte.uedm.dcdigital.interfaces.web.dto.*;
import com.zte.uedm.dcdigital.interfaces.web.vo.*;
import com.zte.uedm.dcdigital.log.annotation.DcOperationLog;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import com.zte.uedm.dcdigital.security.annotation.DcPermission;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.List;

/**
 * 深化设计控制器
 */
@Path("/uportal/item/deepen-design")
@Api(value = "深化设计", tags = {"深化设计接口"})
@Controller
@Slf4j
public class DeepenDesignController {

    @Autowired
    private DeepenDesignQueryService deepenDesignQueryService;

    @Autowired
    private DeepenDesignCommandService deepenDesignCommandService;

    /**
     * 深化实施（包含成果输出）信息查询
     */
    @GET
    @Path("/query-deepen-implementation")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "深化实施（包含成果输出）信息查询", notes = "深化实施（包含成果输出）信息查询", httpMethod = "GET")
    public BaseResult<DeepenImplementationVo> queryDeepenImplementation(@QueryParam("itemId") String itemId) {
        log.info("DeepenDesignController.queryDeepenImplementation start, itemId: {}", itemId);

        DeepenImplementationVo result = deepenDesignQueryService.queryDeepenImplementation(itemId);
        BaseResult<DeepenImplementationVo> response = BaseResult.success(result);
        response.setMessage("成功");
        return response;
    }

    /**
     * 深化实施历史记录查询（按更新时间倒序）
     */
    @POST
    @Path("/query-implementation-history")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "深化实施历史记录查询", notes = "深化实施历史记录查询 按照更新时间倒序", httpMethod = "POST")
    public BaseResult<PageVO<DeepenImplementationHistoryVo>> queryImplementationHistory(@Validated DeepenImplementationHistoryQueryDto queryDto) {
        log.info("DeepenDesignController.queryImplementationHistory start, queryDto: {}", queryDto);

        PageVO<DeepenImplementationHistoryVo> result = deepenDesignQueryService.queryImplementationHistory(queryDto);
        BaseResult<PageVO<DeepenImplementationHistoryVo>> response = BaseResult.success(result);
        response.setMessage("成功");
        return response;
    }

    /**
     * 深化实施（包含成果输出部分）编辑
     */
    @POST
    @Path("/edit-implementation")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "深化实施（包含成果输出部分）编辑", notes = "深化实施（包含成果输出部分）编辑", httpMethod = "POST")
    @DcPermission(value = {"item.deepen.sign.edit"}, checkResource = true)
    @DcOperationLog(method = OperationMethodEnum.UPDATE, module = "module-project-manager",
            operation = "DeepenImplementationEditDescription", targetClass = DeepenImplementationEditDto.class)
    public BaseResult<String> editImplementation(@Validated DeepenImplementationEditDto editDto) {
        log.info("DeepenDesignController.editImplementation start, editDto: {}", editDto);

        deepenDesignCommandService.editImplementation(editDto);
        BaseResult<String> response = BaseResult.success("操作成功");
        response.setMessage("操作成功");
        return response;
    }

    /**
     * 施工图会审--新增会审记录
     */
    @POST
    @Path("/add-review-record")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "施工图会审--新增会审记录", notes = "新增一条施工图会审记录", httpMethod = "POST")
    @DcPermission(value = {"item.construction.drawing.review.add"}, checkResource = true)
    @DcOperationLog(method = OperationMethodEnum.ADD, module = "module-project-manager",
            operation = "ConstructionDrawingReviewAddDescription", targetClass = ConstructionDrawingReviewAddDto.class)
    public BaseResult<String> addReviewRecord(@Validated ConstructionDrawingReviewAddDto addDto) {
        log.info("DeepenDesignController.addReviewRecord start, addDto: {}", addDto);

        deepenDesignCommandService.addReviewRecord(addDto);
        BaseResult<String> response = BaseResult.success();
        response.setMessage("操作成功");
        return response;
    }

    /**
     * 施工图会审--编辑会审记录
     */
    @POST
    @Path("/edit-review-record")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "施工图会审--编辑会审记录", notes = "编辑已有的一条施工图会审记录", httpMethod = "POST")
    @DcPermission(value = {"item.construction.drawing.review.edit"}, checkResource = true)
    @DcOperationLog(method = OperationMethodEnum.UPDATE, module = "module-project-manager",
            operation = "ConstructionDrawingReviewEditDescription", targetClass = ConstructionDrawingReviewEditDto.class)
    public BaseResult<String> editReviewRecord(@Validated ConstructionDrawingReviewEditDto editDto) {
        log.info("DeepenDesignController.editReviewRecord start, editDto: {}", editDto);

        deepenDesignCommandService.editReviewRecord(editDto);
        BaseResult<String> response = BaseResult.success();
        response.setMessage("操作成功");
        return response;
    }

    /**
     * 施工图会审--删除会审记录
     */
    @POST
    @Path("/delete-review-record")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "施工图会审--删除会审记录", notes = "根据会审记录ID删除对应的施工图会审记录", httpMethod = "POST")
    @DcPermission(value = {"item.construction.drawing.review.delete"}, checkResource = true)
    @DcOperationLog(method = OperationMethodEnum.DELETE, module = "module-project-manager",
            operation = "ConstructionDrawingReviewDeleteDescription", targetClass = ConstructionDrawingReviewDeleteDto.class)
    public BaseResult<String> deleteReviewRecord(@Validated ConstructionDrawingReviewDeleteDto deleteDto) {
        log.info("DeepenDesignController.deleteReviewRecord start, deleteDto: {}", deleteDto);

        deepenDesignCommandService.deleteReviewRecord(deleteDto);
        BaseResult<String> response = BaseResult.success();
        response.setMessage("操作成功");
        return response;
    }

    /**
     * 施工图会审--查询项目所有会审记录
     */
    @GET
    @Path("/query-all-record")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "施工图会审--查询项目所有会审记录", notes = "查询某个项目下所有的施工图会审记录", httpMethod = "GET")
    public BaseResult<List<ConstructionDrawingReviewVo>> queryAllRecord(@QueryParam("itemId") String itemId) {
        log.info("DeepenDesignController.queryAllRecord start, itemId: {}", itemId);

        List<ConstructionDrawingReviewVo> result = deepenDesignQueryService.queryAllRecord(itemId);
        BaseResult<List<ConstructionDrawingReviewVo>> response = BaseResult.success(result);
        response.setMessage("成功");
        return response;
    }

    /**
     * 施工图会审--查询项目会审记录详情
     */
    @GET
    @Path("/query-by-record-id")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "施工图会审--查询项目会审记录详情", notes = "根据会审记录ID查询该条记录的详细信息", httpMethod = "GET")
    public BaseResult<ConstructionDrawingReviewVo> queryByRecordId(@QueryParam("id") String id) {
        log.info("DeepenDesignController.queryByRecordId start, id: {}", id);

        ConstructionDrawingReviewVo result = deepenDesignQueryService.queryByRecordId(id);
        BaseResult<ConstructionDrawingReviewVo> response = BaseResult.success(result);
        response.setMessage("成功");
        return response;
    }

    /**
     * 根据项目id查询产品小类
     */
    @GET
    @Path("/query-category-by-item-id")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据项目id查询产品小类", notes = "通过项目ID找到对应的商机，并从该商机的工程量清单中提取所有关联的产品小类ID和pathname", httpMethod = "GET")
    public BaseResult<PageVO<ProductCategorySimpleVo>> queryCategoryByItemId(@QueryParam("id") String id,
                                                                           @QueryParam("pageNo") Integer pageNo,
                                                                           @QueryParam("pageSize") Integer pageSize) {
        log.info("DeepenDesignController.queryCategoryByItemId start, id: {}", id);

        PageVO<ProductCategorySimpleVo> result = deepenDesignQueryService.queryCategoryByItemId(id, pageNo, pageSize);

        return BaseResult.success(result);
    }

    /**
     * 项目概览-工程深化设计信息-信息查询
     */
    @GET
    @Path("/query-overview")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "项目概览-工程深化设计信息-信息查询", notes = "查询项目的工程深化设计相关概览信息", httpMethod = "GET")
    public BaseResult<DeepenDesignOverviewVo> queryOverview(@QueryParam("id") String id) {
        log.info("DeepenDesignController.queryOverview start, id: {}", id);

        DeepenDesignOverviewVo result = deepenDesignQueryService.queryOverview(id);
        BaseResult<DeepenDesignOverviewVo> response = BaseResult.success(result);
        response.setMessage("操作成功");
        return response;
    }

    /**
     * 项目概览-工程深化设计信息-提资信息查询
     */
    @POST
    @Path("/query-submission-information")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "提资信息查询", notes = "根据项目id查询提资信息", httpMethod = "POST")
    public BaseResult<Object> querySubmissionInfoByItemId(ProductUpgradeQueryDto queryDto) {
        log.info("DeepenDesignController.querySubmissionInfoByItemId start, queryDto: {}", queryDto);
        PageVoUpgrade<ProductUpgradeVo> pageList = deepenDesignQueryService.querySubmissionInfoByItemId(queryDto);
        return BaseResult.success(pageList);
    }
}
