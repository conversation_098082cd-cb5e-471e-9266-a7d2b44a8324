package com.zte.uedm.dcdigital.application.project.impl;

import com.zte.uedm.dcdigital.application.project.ProjectCommandService;
import com.zte.uedm.dcdigital.domain.service.ProjectService;
import com.zte.uedm.dcdigital.interfaces.web.dto.LaunchBidDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectAddDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectEditDto;
import com.zte.uedm.dcdigital.common.bean.project.LaunchBiddingInnerVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ProjectCommandServiceImpl implements ProjectCommandService {

    @Autowired
    private ProjectService projectService;

    @Override
    public Boolean addProject(ProjectAddDto addDto) {
        return projectService.addProject(addDto);
    }

    @Override
    public Boolean updateProject(ProjectEditDto updateDto) {
        return projectService.editProject(updateDto);
    }

    @Override
    public Boolean deleteProject(String id) {
        return projectService.deleteProject(id);
    }

    @Override
    public void launchOrUpdateBidding(LaunchBidDto launchBidDto) {
        projectService.launchOrUpdateBidding(launchBidDto);
    }

    @Override
    public void updateLaunchBidById(LaunchBidDto launchBidDto) {
        projectService.updateLaunchBidById(launchBidDto);
    }

    @Override
    public LaunchBiddingInnerVo queryLaunchBidById(String projectId) {
        return projectService.queryLaunchBidById(projectId);
    }

    @Override
    public List<LaunchBiddingInnerVo> queryLaunchBidListByIds(List<String> projectIds) {
        return projectService.queryLaunchBidListByIds(projectIds);
    }
}
