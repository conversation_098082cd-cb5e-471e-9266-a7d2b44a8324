package com.zte.uedm.dcdigital.domain.service;

import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.domain.aggregate.model.DeepenDesignFilesRelationEntity;
import com.zte.uedm.dcdigital.domain.common.valueobj.PageVoUpgrade;
import com.zte.uedm.dcdigital.interfaces.web.dto.*;
import com.zte.uedm.dcdigital.interfaces.web.vo.*;

import java.util.List;

/**
 * 深化设计领域服务接口
 */
public interface DeepenDesignDomainService {

    /**
     * 查询深化实施（包含成果输出）信息
     *
     * @param itemId 项目ID
     * @return 深化实施信息
     */
    DeepenImplementationVo queryDeepenImplementation(String itemId);

    /**
     * 查询深化实施历史记录（按更新时间倒序）
     *
     * @param queryDto 查询条件
     * @return 深化实施历史记录分页列表
     */
    PageVO<DeepenImplementationHistoryVo> queryImplementationHistory(DeepenImplementationHistoryQueryDto queryDto);

    /**
     * 编辑深化实施（包含成果输出部分）
     * 每次更新操作都需要记录历史记录，用于后续追踪和审计
     *
     * @param editDto 编辑信息
     */
    void editImplementation(DeepenImplementationEditDto editDto);

    /**
     * 新增施工图会审记录
     *
     * @param addDto 新增信息
     */
    void addReviewRecord(ConstructionDrawingReviewAddDto addDto);

    /**
     * 编辑施工图会审记录
     *
     * @param editDto 编辑信息
     */
    void editReviewRecord(ConstructionDrawingReviewEditDto editDto);

    /**
     * 删除施工图会审记录
     *
     * @param deleteDto 删除信息
     */
    void deleteReviewRecord(ConstructionDrawingReviewDeleteDto deleteDto);

    /**
     * 查询项目所有施工图会审记录
     *
     * @param itemId 项目ID
     * @return 会审记录列表
     */
    List<ConstructionDrawingReviewVo> queryAllRecord(String itemId);

    /**
     * 根据记录ID查询施工图会审记录详情
     *
     * @param id 会审记录ID
     * @return 会审记录详情
     */
    ConstructionDrawingReviewVo queryByRecordId(String id);

    /**
     * 根据项目ID查询产品小类
     *
     * @param itemId 项目ID
     * @return 产品小类信息列表（包含ID和pathname）
     */
    PageVO<ProductCategorySimpleVo> queryCategoryByItemId(String itemId, Integer pageNo, Integer pageSize);

    /**
     * 项目概览-工程深化设计信息-信息查询
     *
     * @param id 项目ID
     * @return 工程深化设计概览信息
     */
    DeepenDesignOverviewVo queryOverview(String id);

    /**
     * 内部调用接口查询深化实施（包含成果输出）信息
     *
     * @param itemId 项目ID
     * @return 深化实施信息
     */
    DeepenImplementationVo innerQueryDeepenImplementation(String itemId);

    /**
     * 项目概览-工程深化设计信息-提资信息查询
     * @param queryDto 项目ID
     * */
    PageVoUpgrade<ProductUpgradeVo> querySubmissionInfoByItemId(ProductUpgradeQueryDto queryDto);

    //新增提产品资关联流程信息
    int addDeepenDesignFilesRelation(DeepenDesignFilesRelationEntity deepenDesignFilesRelationEntity);

    //更新提产品资关联流程信息
    int updateDeepenDesignFilesRelation(DeepenDesignFilesRelationEntity deepenDesignFilesRelationEntity);

    //查询提产品资关联流程信息
    List<DeepenDesignFilesRelationEntity> queryDeepenDesignFilesRelation(DeepenDesignFilesRelationEntity deepenDesignFilesRelationEntity);

    List<DeepenImplementationVo> queryInnerDeepenImplementationList(List<String> itemIds);
}
