/* Started by AICoder, pid:kc8210d060j7efb143100818f0ce65354db39981 */
package com.zte.uedm.dcdigital.infrastructure.repository.converter;

import com.zte.uedm.dcdigital.domain.aggregate.model.LaunchBiddingEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.po.LaunchBiddingPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface LaunchBiddingConvert {
    /**
     * 单例实例，通过MapStruct的工厂方法获取。
     */
    LaunchBiddingConvert INSTANCE = Mappers.getMapper(LaunchBiddingConvert.class);

    /**
     * 将持久化对象（PO）转换为实体对象（Entity）。
     *
     * @param launchBiddingPo 持久化对象，包含招标信息。
     * @return LaunchBiddingEntity 实体对象，包含招标信息。
     */
    @Mappings({})
    LaunchBiddingEntity poToEntity(LaunchBiddingPo launchBiddingPo);


    @Mappings({})
    List<LaunchBiddingEntity> poListToEntityList(List<LaunchBiddingPo> launchBiddingPo);

    /**
     * 将实体对象（Entity）转换为持久化对象（PO）。
     *
     * @param launchBiddingEntity 实体对象，包含招标信息。
     * @return LaunchBiddingPo 持久化对象，包含招标信息。
     */
    @Mappings({})
    LaunchBiddingPo entityToPo(LaunchBiddingEntity launchBiddingEntity);
}
/* Ended by AICoder, pid:kc8210d060j7efb143100818f0ce65354db39981 */