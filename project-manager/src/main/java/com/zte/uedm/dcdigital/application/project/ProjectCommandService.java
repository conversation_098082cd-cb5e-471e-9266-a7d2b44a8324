package com.zte.uedm.dcdigital.application.project;

import com.zte.uedm.dcdigital.common.bean.project.ShortBrandUpdateDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.LaunchBidDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectAddDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectEditDto;
import com.zte.uedm.dcdigital.common.bean.project.LaunchBiddingInnerVo;

import java.util.List;

public interface ProjectCommandService {
    Boolean addProject(ProjectAddDto addDto);

    Boolean updateProject(ProjectEditDto updateDto);

    Boolean deleteProject(String id);

    /* Started by AICoder, pid:s5b36ua7b8a0e9914bae0bb690a7ca0b8dd6e95c */
    /**
     * 启动或更新一个招标。
     *
     * @param launchBidDto 包含招标信息的数据传输对象，用于启动或更新招标。
     */
    void launchOrUpdateBidding(LaunchBidDto launchBidDto);
    /**
     * RPC调用根据项目id更新招标信息。
     *
     * @param launchBidDto 包含招标信息的数据传输对象，用于更新招标。
     */
    void updateLaunchBidById(LaunchBidDto launchBidDto);
    /**
     * RPC调用根据项目id获取招标信息。
     *
     * @param projectId 项目id。
     */
    LaunchBiddingInnerVo queryLaunchBidById(String projectId);

    List<LaunchBiddingInnerVo> queryLaunchBidListByIds(List<String> projectIds);
    /* Ended by AICoder, pid:s5b36ua7b8a0e9914bae0bb690a7ca0b8dd6e95c */

}
