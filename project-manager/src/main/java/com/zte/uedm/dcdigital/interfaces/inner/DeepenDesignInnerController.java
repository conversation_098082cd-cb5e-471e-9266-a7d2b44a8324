/* Started by AICoder, pid:a4e41367b0c64fe140b60a2100ef7f5faad7e812 */
package com.zte.uedm.dcdigital.interfaces.inner;

import com.zte.uedm.dcdigital.application.deependesign.executor.DeepenDesignCommandService;
import com.zte.uedm.dcdigital.application.deependesign.executor.DeepenDesignQueryService;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.domain.aggregate.model.DeepenDesignFilesRelationEntity;
import com.zte.uedm.dcdigital.domain.service.DeepenDesignDomainService;
import com.zte.uedm.dcdigital.interfaces.web.vo.*;
import com.zte.uedm.dcdigital.sdk.project.dto.DeepenDesignFilesRelationInnerAddDto;
import com.zte.uedm.dcdigital.sdk.project.vo.DeepenDesignFilesRelationInnerVo;
import com.zte.uedm.dcdigital.sdk.project.vo.DeepenImplementationInnerVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 深化设计内部调用控制器
 */
@Path("/deepen-design-inner")
@Api(value = "深化设计", tags = {"深化设计内部调用接口"})
@Controller
@Slf4j
public class DeepenDesignInnerController {

    @Autowired
    private DeepenDesignQueryService deepenDesignQueryService;

    @Autowired
    private DeepenDesignCommandService deepenDesignCommandService;

    @Autowired
    private DeepenDesignDomainService deepenDesignDomainService;

    /**
     * 深化实施（包含成果输出）信息查询
     */
    @GET
    @Path("/deepen-implementation-detail-by-itemId")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "深化实施（包含成果输出）信息查询",
            notes = "深化实施（包含成果输出）信息查询",
            httpMethod = "GET")
    public BaseResult<DeepenImplementationInnerVo> innerQueryDeepenImplementation(
            @QueryParam("itemId") String itemId) {
        log.info("DeepenDesignInnerController.innerQueryDeepenImplementation start, itemId: {}", itemId);

        DeepenImplementationVo result = deepenDesignQueryService.innerQueryDeepenImplementation(itemId);
        log.info("DeepenDesignInnerController.innerQueryDeepenImplementation end, result: {}", result);

        DeepenImplementationInnerVo innerVo = new DeepenImplementationInnerVo();
        if (result == null) {
            return BaseResult.success(innerVo);
        }

        BeanUtils.copyProperties(result, innerVo);
        return BaseResult.success(innerVo);
    }

    /**
     * 深化实施（包含成果输出）信息查询
     */
    @POST
    @Path("/deepen-implementation-list-by-itemIds")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "深化实施（包含成果输出）信息查询",
            notes = "深化实施（包含成果输出）信息查询",
            httpMethod = "POST")
    public BaseResult<List<DeepenImplementationInnerVo>> queryInnerDeepenImplementationList(
            @QueryParam("itemIds") List<String> itemIds) {
        log.info("DeepenDesignInnerController.queryInnerDeepenImplementationList start, itemIds: {}", itemIds);

        List<DeepenImplementationVo> result = deepenDesignQueryService.queryInnerDeepenImplementationList(itemIds);

        List<DeepenImplementationInnerVo> innerVoList = result.stream()
                .map(this::convertToInnerVo)
                .collect(Collectors.toList());

        return BaseResult.success(innerVoList);
    }

    /**
     * 新增深化实施 产品提资任务关联流程信息
     */
    @POST
    @Path("/add-files-relation")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "新增产品提资任务关联流程信息",
            notes = "新增产品提资任务关联流程信息",
            httpMethod = "POST")
    public BaseResult<Void> addDeepenDesignFilesRelation(DeepenDesignFilesRelationInnerAddDto innerAddDto) {
        log.info("DeepenDesignInnerController.addDeepenDesignFilesRelation start, innerAddDto: {}", innerAddDto);
        DeepenDesignFilesRelationEntity deepenDesignFilesRelationEntity=new DeepenDesignFilesRelationEntity();
        BeanUtils.copyProperties(innerAddDto, deepenDesignFilesRelationEntity);
        log.info("DeepenDesignInnerController.addDeepenDesignFilesRelation end, deepenDesignFilesRelationEntity: {}", deepenDesignFilesRelationEntity);
        deepenDesignDomainService.addDeepenDesignFilesRelation(deepenDesignFilesRelationEntity);
        return BaseResult.success();
    }

    /**
     * 更新深化实施 产品提资任务关联流程信息
     */
    @POST
    @Path("/update-files-relation")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "新增产品提资任务关联流程信息",
            notes = "新增产品提资任务关联流程信息",
            httpMethod = "POST")
    public BaseResult<Void> updateDeepenDesignFilesRelation(DeepenDesignFilesRelationInnerAddDto innerAddDto) {
        log.info("DeepenDesignInnerController.updateDeepenDesignFilesRelation start, innerAddDto: {}", innerAddDto);
        DeepenDesignFilesRelationEntity deepenDesignFilesRelationEntity=new DeepenDesignFilesRelationEntity();
        BeanUtils.copyProperties(innerAddDto, deepenDesignFilesRelationEntity);
        log.info("DeepenDesignInnerController.updateDeepenDesignFilesRelation end, deepenDesignFilesRelationEntity: {}", deepenDesignFilesRelationEntity);
        deepenDesignDomainService.updateDeepenDesignFilesRelation(deepenDesignFilesRelationEntity);
        return BaseResult.success();
    }

    /**
     * 查询深化实施 产品提资任务关联流程信息
     */
    @POST
    @Path("/query-files-relation")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "查询产品提资任务关联流程信息",
            notes = "查询产品提资任务关联流程信息",
            httpMethod = "POST")
    public BaseResult<List<DeepenDesignFilesRelationInnerVo>> queryDeepenDesignFilesRelation(DeepenDesignFilesRelationInnerAddDto innerQueryDto) {
        log.info("DeepenDesignInnerController.queryDeepenDesignFilesRelation start, innerQueryDto: {}", innerQueryDto);
        DeepenDesignFilesRelationEntity deepenDesignFilesRelationEntity=new DeepenDesignFilesRelationEntity();
        BeanUtils.copyProperties(innerQueryDto, deepenDesignFilesRelationEntity);
        log.info("DeepenDesignInnerController.queryDeepenDesignFilesRelation end, deepenDesignFilesRelationEntity: {}", deepenDesignFilesRelationEntity);
        List<DeepenDesignFilesRelationEntity> entityList=deepenDesignDomainService.queryDeepenDesignFilesRelation(deepenDesignFilesRelationEntity);
        log.info("entityList:{}",entityList);
        // 将entityList转换为innerVo列表
        List<DeepenDesignFilesRelationInnerVo> innerVoList = entityList.stream().map(entity -> {
            DeepenDesignFilesRelationInnerVo innerVo = new DeepenDesignFilesRelationInnerVo();
            BeanUtils.copyProperties(entity, innerVo);
            return innerVo;
        }).collect(Collectors.toList());
        return BaseResult.success(innerVoList);
    }
    // 转换方法：DeepenImplementationVo -> DeepenImplementationInnerVo
    private DeepenImplementationInnerVo convertToInnerVo(DeepenImplementationVo vo) {
        if (vo == null) {
            return null;
        }

        DeepenImplementationInnerVo innerVo = new DeepenImplementationInnerVo();

        innerVo.setId(vo.getId());
        innerVo.setDesignDirector(vo.getDesignDirector());
        innerVo.setStartTime(vo.getStartTime());
        innerVo.setRequestInformationCompletionTime(vo.getRequestInformationCompletionTime());
        innerVo.setActualInformationCompletionTime(vo.getActualInformationCompletionTime());
        innerVo.setDeepenDesignStartTime(vo.getDeepenDesignStartTime());
        innerVo.setDeepensDesignEndTime(vo.getDeepensDesignEndTime());
        innerVo.setCombinedImageStartTime(vo.getCombinedImageStartTime());
        innerVo.setCombinedImageEndTime(vo.getCombinedImageEndTime());

        // 字段名不同但含义一致的字段映射
        innerVo.setBimSpecialDesignStartTime(vo.getBIMSpecialDesignStartTime());
        innerVo.setBimSpecialDesignEndTime(vo.getBIMSpecialDesignEndTime());

        innerVo.setFirstBatchMaterialPreparationOutputTime(vo.getFirstBatchMaterialPreparationOutputTime());
        innerVo.setLongPeriodMaterialListOutputTime(vo.getLongPeriodMaterialListOutputTime());
        innerVo.setOverallMaterialPreparationLockTime(vo.getOverallMaterialPreparationLockTime());
        innerVo.setEngineeringDeepenProgress(vo.getEngineeringDeepenProgress());

        // 状态 VO 转换
        if (vo.getDeepenDesignStatus() != null) {
            DeepenImplementationInnerVo.DeepenDesignStatusVo statusInnerVo =
                    new DeepenImplementationInnerVo.DeepenDesignStatusVo();
            statusInnerVo.setId(vo.getDeepenDesignStatus().getId());
            statusInnerVo.setName(vo.getDeepenDesignStatus().getName());
            innerVo.setDeepenDesignStatus(statusInnerVo);
        }

        innerVo.setDelayReason(vo.getDelayReason());
        innerVo.setModifyDeepenDesignStartTime(vo.getModifyDeepenDesignStartTime());
        innerVo.setModifyDeepenDesignEndTime(vo.getModifyDeepenDesignEndTime());
        innerVo.setConstructionDrawingCalibrationStartTime(vo.getConstructionDrawingCalibrationStartTime());
        innerVo.setConstructionDrawingCalibrationEndTime(vo.getConstructionDrawingCalibrationEndTime());
        innerVo.setConstructionDrawingRevisionStartTime(vo.getConstructionDrawingRevisionStartTime());

        return innerVo;
    }
}

/* Ended by AICoder, pid:a4e41367b0c64fe140b60a2100ef7f5faad7e812 */