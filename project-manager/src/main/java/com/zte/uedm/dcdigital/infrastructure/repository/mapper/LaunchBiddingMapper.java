package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.LaunchBiddingPo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface LaunchBiddingMapper extends BaseMapper<LaunchBiddingPo> {

    List<LaunchBiddingPo> queryLaunchBidListByIds(List<String> projectIds);
}
