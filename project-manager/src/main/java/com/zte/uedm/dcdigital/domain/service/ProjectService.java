package com.zte.uedm.dcdigital.domain.service;

/* Started by AICoder, pid:y3907s9ce9ec52f14cd209ac304c178578e4b756 */
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.project.ProjectDetailInfoVo;
import com.zte.uedm.dcdigital.common.bean.project.ProjectProductSupportsVo;
import com.zte.uedm.dcdigital.common.bean.project.ShortBrandUpdateDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.LaunchBidDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectAddDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectEditDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.*;
import com.zte.uedm.dcdigital.common.bean.project.LaunchBiddingInnerVo;

import java.util.List;

/**
 * 项目服务接口，定义了与项目相关的所有操作。
 */
public interface ProjectService {

    /**
     * 获取所有项目阶段列表。
     *
     * @return 包含所有项目阶段的列表。
     */
    List<StageVo> getProjectStageList();

    /**
     * 根据客户名称获取客户列表。
     *
     * @param name 客户名称。
     * @return 包含匹配客户的列表。
     */
    List<CustomerVo> selectProjectCustomers(String name);

    /**
     * 根据查询条件获取项目列表。
     *
     * @param queryDto 查询条件。
     * @return 包含匹配项目的分页结果。
     */
    PageVO<ProjectVo> selectProject(ProjectQueryDto queryDto);

    List<ProjectVo> selectProject();

    /**
     * 根据项目ID获取项目详情。
     *
     * @param id 项目ID。
     * @return 包含项目详情的对象。
     */
    ProjectDetailVo getDetail(String id);

    /**
     * 根据地区名称获取地区树。
     *
     * @param areaName 地区名称。
     * @return 包含地区树的列表。
     */
    List<AreaTreeVo> selectAreaTree(String areaName);

    /**
     * 添加新项目。
     *
     * @param addDto 新项目信息。
     * @return 操作是否成功。
     */
    Boolean addProject(ProjectAddDto addDto);

    /**
     * 编辑现有项目。
     *
     * @param updateDto 更新后的项目信息。
     * @return 操作是否成功。
     */
    Boolean editProject(ProjectEditDto updateDto);

    /**
     * 删除项目。
     *
     * @param id 项目ID。
     * @return 操作是否成功。
     */
    Boolean deleteProject(String id);

    /**
     * 根据关键字获取地区和项目树。
     *
     * @param keyword 关键字。
     * @return 包含地区和项目树的列表。
     */
    List<AreaProjectTreeVo> getAreaProjectTree(String keyword);

    /* Started by AICoder, pid:c2b72g5052419ef14b0408ce10ac010619f7e9b0 */
    /**
     * 根据项目ID查询项目的详细信息。
     *
     * @param id 项目ID，用于查询具体的项目详情。
     * @return 包含查询结果的 ProjectDetailInfoVo 对象。如果未找到项目，返回的对象将包含空值。
     */
    ProjectDetailInfoVo queryProjectDetail(String id);
    /* Ended by AICoder, pid:c2b72g5052419ef14b0408ce10ac010619f7e9b0 */

    /* Started by AICoder, pid:w5b36ia7b8d0e9914bae0bb690a7ca1b8dd4e95c */
    /**
     * 根据指定的ID获取启动招标的信息。
     *
     * @param id 招标记录的唯一标识符，用于查询特定的招标信息。
     * @return LaunchBiddingVo 包含招标详细信息的对象。如果未找到对应的招标记录，则可能返回null或空对象（具体取决于实现）。
     */
    LaunchBiddingVo queryBiddingInformationById(String id);

    /**
     * 启动或更新一个招标。
     *
     * @param launchBidDto 包含招标信息的数据传输对象，用于启动或更新招标。
     */
    void launchOrUpdateBidding(LaunchBidDto launchBidDto);
    /**
     * RPC调用根据项目id更新招标信息。
     *
     * @param launchBidDto 包含招标信息的数据传输对象，用于更新招标。
     */
    void updateLaunchBidById(LaunchBidDto launchBidDto);

    List<String> queryProjectIdByAreaId(String id);

    LaunchBiddingInnerVo queryLaunchBidById(String projectId);

    List<ProjectProductSupportsVo> queryProjectProductSupport(String projectId, List<String> productCategoryIds);

    List<LaunchBiddingInnerVo> queryLaunchBidListByIds(List<String> projectIds);
    /* Ended by AICoder, pid:w5b36ia7b8d0e9914bae0bb690a7ca1b8dd4e95c */

}
/* Ended by AICoder, pid:y3907s9ce9ec52f14cd209ac304c178578e4b756 */
