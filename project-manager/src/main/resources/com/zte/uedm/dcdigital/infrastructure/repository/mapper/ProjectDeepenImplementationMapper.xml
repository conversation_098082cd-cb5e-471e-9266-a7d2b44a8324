<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProjectDeepenImplementationMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.zte.uedm.dcdigital.infrastructure.repository.po.ProjectDeepenImplementationPo">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="design_director" property="designDirector" jdbcType="VARCHAR"/>
        <result column="start_time" property="startTime" jdbcType="VARCHAR"/>
        <result column="request_information_completion_time" property="requestInformationCompletionTime" jdbcType="VARCHAR"/>
        <result column="actual_information_completion_time" property="actualInformationCompletionTime" jdbcType="VARCHAR"/>
        <result column="deepen_design_start_time" property="deepenDesignStartTime" jdbcType="VARCHAR"/>
        <result column="deepens_design_end_time" property="deepensDesignEndTime" jdbcType="VARCHAR"/>
        <result column="combined_image_start_time" property="combinedImageStartTime" jdbcType="VARCHAR"/>
        <result column="combined_image_end_time" property="combinedImageEndTime" jdbcType="VARCHAR"/>
        <result column="BIM_special_design_start_time" property="bIMSpecialDesignStartTime" jdbcType="VARCHAR"/>
        <result column="BIM_special_design_end_time" property="bIMSpecialDesignEndTime" jdbcType="VARCHAR"/>
        <result column="first_batch_material_preparation_output_time" property="firstBatchMaterialPreparationOutputTime" jdbcType="VARCHAR"/>
        <result column="long_period_material_list_output_time" property="longPeriodMaterialListOutputTime" jdbcType="VARCHAR"/>
        <result column="overall_material_preparation_lock_time" property="overallMaterialPreparationLockTime" jdbcType="VARCHAR"/>
        <result column="engineering_deepen_progress" property="engineeringDeepenProgress" jdbcType="INTEGER"/>
        <result column="deepen_design_status" property="deepenDesignStatus" jdbcType="VARCHAR"/>
        <result column="delay_reason" property="delayReason" jdbcType="VARCHAR"/>
        <result column="construction_drawing_revision_start_time" property="constructionDrawingRevisionStartTime" jdbcType="VARCHAR"/>
        <result column="modify_deepen_design_start_time" property="modifyDeepenDesignStartTime" jdbcType="VARCHAR"/>
        <result column="modify_deepen_design_end_time" property="modifyDeepenDesignEndTime" jdbcType="VARCHAR"/>
        <result column="construction_drawing_calibration_start_time" property="constructionDrawingCalibrationStartTime" jdbcType="VARCHAR"/>
        <result column="construction_drawing_calibration_end_time" property="constructionDrawingCalibrationEndTime" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="VARCHAR"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 根据项目ID查询深化实施信息 -->
    <select id="selectByItemId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
            id, design_director, start_time, request_information_completion_time,
            actual_information_completion_time, deepen_design_start_time, deepens_design_end_time,
            combined_image_start_time, combined_image_end_time, BIM_special_design_start_time,
            BIM_special_design_end_time, first_batch_material_preparation_output_time,
            long_period_material_list_output_time, overall_material_preparation_lock_time,
            engineering_deepen_progress, deepen_design_status, delay_reason,
            construction_drawing_revision_start_time, modify_deepen_design_start_time,
            modify_deepen_design_end_time, construction_drawing_calibration_start_time,
            construction_drawing_calibration_end_time, create_by, create_time, update_by, update_time
        FROM project_deepen_implementation 
        WHERE id = #{itemId}
    </select>
    <!-- 查询深化实施信息列表 -->
    <!-- Started by AICoder, pid:t8d077a618naa5e14d9b08f180ffe45b6df037c8 -->
    <select id="selectDeepenImplementationListByItemIds" resultMap="BaseResultMap">
        <!-- 列选择部分 -->
        <trim prefix="SELECT" suffixOverrides=",">
            <!-- 列名按功能分组排列 -->
            id,
            design_director,
            start_time,

            -- 时间相关字段 --
            request_information_completion_time,
            actual_information_completion_time,
            deepen_design_start_time,
            deepens_design_end_time,
            combined_image_start_time,
            combined_image_end_time,
            BIM_special_design_start_time,
            BIM_special_design_end_time,
            first_batch_material_preparation_output_time,
            long_period_material_list_output_time,
            overall_material_preparation_lock_time,

            -- 进度状态字段 --
            engineering_deepen_progress,
            deepen_design_status,
            delay_reason,

            -- 修改相关时间 --
            construction_drawing_revision_start_time,
            modify_deepen_design_start_time,
            modify_deepen_design_end_time,
            construction_drawing_calibration_start_time,
            construction_drawing_calibration_end_time,

            -- 审计字段 --
            create_by,
            create_time,
            update_by,
            update_time
        </trim>

        FROM project_deepen_implementation

        <where>
            id IN
            <foreach item="item" collection="itemIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>

    <!-- Ended by AICoder, pid:t8d077a618naa5e14d9b08f180ffe45b6df037c8 -->

    <!-- 插入深化实施记录 -->
    <insert id="insertDeepenImplementation" parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.ProjectDeepenImplementationPo">
        INSERT INTO project_deepen_implementation (
            id, design_director, start_time, request_information_completion_time,
            actual_information_completion_time, deepen_design_start_time, deepens_design_end_time,
            combined_image_start_time, combined_image_end_time, BIM_special_design_start_time,
            BIM_special_design_end_time, first_batch_material_preparation_output_time,
            long_period_material_list_output_time, overall_material_preparation_lock_time,
            engineering_deepen_progress, deepen_design_status, delay_reason,
            construction_drawing_revision_start_time, modify_deepen_design_start_time,
            modify_deepen_design_end_time, construction_drawing_calibration_start_time,
            construction_drawing_calibration_end_time, create_by, create_time, update_by, update_time
        ) VALUES (
            #{id}, #{designDirector}, #{startTime}, #{requestInformationCompletionTime},
            #{actualInformationCompletionTime}, #{deepenDesignStartTime}, #{deepensDesignEndTime},
            #{combinedImageStartTime}, #{combinedImageEndTime}, #{bIMSpecialDesignStartTime},
            #{bIMSpecialDesignEndTime}, #{firstBatchMaterialPreparationOutputTime},
            #{longPeriodMaterialListOutputTime}, #{overallMaterialPreparationLockTime},
            #{engineeringDeepenProgress}, #{deepenDesignStatus}, #{delayReason},
            #{constructionDrawingRevisionStartTime}, #{modifyDeepenDesignStartTime},
            #{modifyDeepenDesignEndTime}, #{constructionDrawingCalibrationStartTime},
            #{constructionDrawingCalibrationEndTime}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime}
        )
    </insert>

    <!-- 根据ID更新深化实施记录 -->
    <update id="updateDeepenImplementationById" parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.ProjectDeepenImplementationPo">
        UPDATE project_deepen_implementation SET
            design_director = #{designDirector},
            start_time = #{startTime},
            request_information_completion_time = #{requestInformationCompletionTime},
            actual_information_completion_time = #{actualInformationCompletionTime},
            deepen_design_start_time = #{deepenDesignStartTime},
            deepens_design_end_time = #{deepensDesignEndTime},
            combined_image_start_time = #{combinedImageStartTime},
            combined_image_end_time = #{combinedImageEndTime},
            BIM_special_design_start_time = #{bIMSpecialDesignStartTime},
            BIM_special_design_end_time = #{bIMSpecialDesignEndTime},
            first_batch_material_preparation_output_time = #{firstBatchMaterialPreparationOutputTime},
            long_period_material_list_output_time = #{longPeriodMaterialListOutputTime},
            overall_material_preparation_lock_time = #{overallMaterialPreparationLockTime},
            engineering_deepen_progress = #{engineeringDeepenProgress},
            deepen_design_status = #{deepenDesignStatus},
            delay_reason = #{delayReason},
            construction_drawing_revision_start_time = #{constructionDrawingRevisionStartTime},
            modify_deepen_design_start_time = #{modifyDeepenDesignStartTime},
            modify_deepen_design_end_time = #{modifyDeepenDesignEndTime},
            construction_drawing_calibration_start_time = #{constructionDrawingCalibrationStartTime},
            construction_drawing_calibration_end_time = #{constructionDrawingCalibrationEndTime},
            update_by = #{updateBy},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

</mapper>
