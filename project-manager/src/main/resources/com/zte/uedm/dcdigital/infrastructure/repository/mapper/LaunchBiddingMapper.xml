<?xml version="1.0" encoding="UTF-8"?>
<!-- Started by AICoder, pid:5a725wabb3793fb147dd0a1a80cd6f579b636fd8 -->
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.LaunchBiddingMapper">

    <!-- 字段映射关系 -->
    <resultMap id="BaseResultMap" type="com.zte.uedm.dcdigital.infrastructure.repository.po.LaunchBiddingPo">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="bid_issuing_time" property="bidIssuingTime" jdbcType="VARCHAR"/>
        <result column="clarify_submission_time" property="clarifySubmissionTime" jdbcType="VARCHAR"/>
        <result column="clarify_submission_complete_time" property="clarifySubmissionCompleteTime" jdbcType="VARCHAR"/>
        <result column="configure_manifest_lock_time" property="configureManifestLockTime" jdbcType="VARCHAR"/>
        <result column="configure_manifest_lock_time_complete_time" property="configureManifestLockTimeCompleteTime" jdbcType="VARCHAR"/>
        <result column="bidding_documents_finalization_time" property="biddingDocumentsFinalizationTime" jdbcType="VARCHAR"/>
        <result column="bidding_documents_finalization_completion_time" property="biddingDocumentsFinalizationCompletionTime" jdbcType="VARCHAR"/>
        <result column="bid_submission_time" property="bidSubmissionTime" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="VARCHAR"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 查询接口：根据 projectIds 查询 -->
    <select id="queryLaunchBidListByIds" resultMap="BaseResultMap">
        <trim prefix="SELECT" suffixOverrides=",">
            id,
            bid_issuing_time,
            clarify_submission_time,
            clarify_submission_complete_time,
            configure_manifest_lock_time,
            configure_manifest_lock_time_complete_time,
            bidding_documents_finalization_time,
            bidding_documents_finalization_completion_time,
            bid_submission_time,
            create_by,
            create_time,
            update_by,
            update_time
        </trim>

        FROM launch_bidding

        <where>
            id IN
            <foreach item="projectId" collection="projectIds" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </where>
    </select>

</mapper>

        <!-- Ended by AICoder, pid:5a725wabb3793fb147dd0a1a80cd6f579b636fd8 -->