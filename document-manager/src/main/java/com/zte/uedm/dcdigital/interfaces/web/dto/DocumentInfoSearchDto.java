package com.zte.uedm.dcdigital.interfaces.web.dto;


/* Started by AICoder, pid:b9929vb6feg5550144210928f0d012446ef0edbb */
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
@ToString
public class DocumentInfoSearchDto {


    /**
     * 文档所属Id
     */
    private String ascriptionId;

    /**
     * 搜索文档名称
     */
    private String name;
    /**
     * 搜索附件名称
     */
    private String attachmentName;
    /**
     * 模糊查询与精准查询区分标志 false为模糊查询 true为精准查询
     */
    private Boolean flag = false;
    /**
     * 文档小类类型
     */
    private List<String> documentCategoryIds;

    /**
     * 开始时间 (更新时间开始范围）
     */
    private String startTime;

    /**
     * 结束时间（更新时间结束范围）
     */
    private String endTime;

    private String sort;

    private String order;

    /**
     * 第几页
     */
    @NotNull
    private Integer pageNum;

    /**
     * 每页数量
     */
    @NotNull
    private Integer pageSize;
}

/* Ended by AICoder, pid:b9929vb6feg5550144210928f0d012446ef0edbb */