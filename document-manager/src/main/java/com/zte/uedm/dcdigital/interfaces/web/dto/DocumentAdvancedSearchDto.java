package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/* Started by AICoder, pid:h342a7bf4b633ff144b008c5702a333a6007743d */
@Setter
@Getter
@ToString
public class DocumentAdvancedSearchDto {

    /**
     * 搜索文档名称
     */
    private String name;
    /**
     * 搜索附件名称
     */
    private String attachmentName;
    /**
     * 文档大类类型，1:产品文档，2:商机文档, 3:项目文档
     */
    private Integer type;

    /**
     * 文档小类ID列表
     */
    private List<String> documentCategoryIds;

    /**
     * 产品小类ID列表
     */
    private List<String> productCategoryIds;

    /**
     * 项目ID列表
     */
    private List<String> projectIds;

    /**
     * 第几页
     */
    private Integer pageNum;

    /**
     * 每页数量
     */
    private Integer pageSize;
}

/* Ended by AICoder, pid:h342a7bf4b633ff144b008c5702a333a6007743d */
