/* Started by AICoder, pid:11f040ea70lfb96141b30935a15daf30424821b8 */
package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.document.BidAnalysisContentVo;
import com.zte.uedm.dcdigital.common.bean.document.DocumentInfoVo;
import com.zte.uedm.dcdigital.common.bean.document.FileInfoVo;
import com.zte.uedm.dcdigital.common.bean.document.ProductUpgradeFileVo;
import com.zte.uedm.dcdigital.common.bean.enums.IdNameBean;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.domain.common.valueobj.DocumentInfoObj;
import com.zte.uedm.dcdigital.domain.aggregate.repository.DocumentInfoRepository;
import com.zte.uedm.dcdigital.domain.common.valueobj.DocumentRelationObj;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.DocumentInfoConverter;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.DocumentInfoMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.DocumentInfoTempMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.DocumentInfoDetailPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.DocumentInfoPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.DocumentInfoTempPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocumentAdvancedSearchDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocumentCommonSearchDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocumentInfoQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocumentInfoSearchDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.DocumentInfoDetailVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.DocumentSearchVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * DocumentInfoRepositoryImpl 类实现了 DocumentInfoRepository 接口，负责处理文档信息的持久化操作。
 */
@Slf4j
@Repository
public class DocumentInfoRepositoryImpl extends ServiceImpl<DocumentInfoMapper, DocumentInfoPo>
        implements DocumentInfoRepository {

    @Autowired
    private DocumentInfoMapper documentInfoMapper;

    @Autowired
    private DocumentInfoTempMapper documentInfoTempMapper;

    /**
     * 添加新的文档信息。
     * @param obj 文档信息对象
     */
    @Override
    public void addDocumentInfo(DocumentInfoObj obj) {
        // 将业务对象转换为持久化对象并插入数据库
        documentInfoMapper.insert(DocumentInfoConverter.INSTANCE.convert2Po(obj));
    }

    /**
     * 编辑现有的文档信息。
     * @param obj 文档信息对象
     */
    @Override
    public void editDocumentInfo(DocumentInfoObj obj) {
        // 更新文档信息
        documentInfoMapper.updateById(DocumentInfoConverter.INSTANCE.convert2Po(obj));
    }

    /**
     * 添加文档ID和文件ID关联信息。
     * @param id
     * @param fileIds
     */
    @Override
    public void addFileIds(String id, List<String> fileIds) {
        documentInfoMapper.addFileIds(id, fileIds);
    }

    /**
     * 根据id删除文档关联关系。
     * @param id
     */
    @Override
    public void deleteByDocumentId(String id) {
        documentInfoMapper.deleteByDocumentId(id);
    }

    /**
     * 根据ID查询文档信息。
     * @param id 文档ID
     * @return 文档信息对象
     */
    @Override
    public DocumentInfoObj selectById(String id) {
        DocumentInfoPo po = documentInfoMapper.selectById(id);
        if (po != null) {
            List<FileInfoVo> fileInfoVoList = documentInfoMapper.selectFileInfosById(id);
            List<String> fileIds = fileInfoVoList.stream().map(FileInfoVo::getFileId).collect(Collectors.toList());
            // 将持久化对象转换为业务对象
            return DocumentInfoConverter.INSTANCE.convert2Obj(po, fileIds);
        }
        return null;
    }



    /**
     * 根据ID和名称查询文档数量用于重名校验。
     * @param id 文档ID
     * @param name 文档名称
     * @return 文档重名数量
     */
    @Override
    public int selectByIdAndName(String id, String name) {
        return documentInfoMapper.selectByIdAndName(id, name);
    }

    /**
     * 根据ID删除文档信息。
     * @param id 文档ID
     */
    @Override
    public void deleteById(String id) {
        documentInfoMapper.deleteById(id);
    }

    /**
     * 分页查询文档信息列表。
     * @param dto 查询条件
     * @return 分页结果
     */
    @Override
    public PageVO<DocumentInfoVo> queryList(DocumentInfoSearchDto dto) {
        Integer pageNum = dto.getPageNum();
        Integer pageSize = dto.getPageSize();
        if (pageNum == null || pageSize == null) {
            // 参数无效时返回空的分页信息
            return new PageVO<>();
        }
        //Page<DocumentInfoVo> page = PageHelper.startPage(pageNum, pageSize);
        //log.info("page={}",page);
        List<DocumentInfoVo> list = documentInfoMapper.queryByCondition(dto);
        for (DocumentInfoVo documentInfoVo : list) {
            List<String> urlList = JSON.parseArray(documentInfoVo.getUrls(), String.class);
            documentInfoVo.setUrlList(urlList);
            ////增加显示
            documentInfoVo.setUrlBeanList(getUrlBeansOptimized(urlList));
            List<FileInfoVo> fileInfoVoList = documentInfoMapper.selectFileInfosById(documentInfoVo.getId());
            documentInfoVo.setFileInfos(fileInfoVoList);
        }
        if(dto.getFlag()){
            list = searchByAccurate(list, dto.getName(), dto.getAttachmentName());
        }else {
            list = searchByFuzzy(list, dto.getName());
        }
        //分页操作
        int total = list.size();
        int fromIndex = (pageNum - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);
        List<DocumentInfoVo> pageList = list.subList(fromIndex, toIndex);
        PageVO<DocumentInfoVo> pageInfo = new PageVO<>(list.size(), pageList);
        log.info("pageInfo={}",pageInfo);
        return pageInfo;
    }
    /* Started by AICoder, pid:38845mf08aza46214c1608a8b030b52422640bce */
    public List<DocumentInfoVo> searchByFuzzy(List<DocumentInfoVo> documents, String key){
        if (key == null || key.trim().isEmpty()) {
            return documents; // 如果key为空，返回所有数据
        }
        String lowerKey = key.toLowerCase(); // 转换为小写用于不区分大小写的匹配
        return documents.stream()
                .filter(doc -> {
                    // 检查文档名称是否匹配
                    boolean nameMatches = doc.getName().toLowerCase().contains(lowerKey);
                    // 检查文件列表中是否有文件名匹配
                    boolean fileMatches = doc.getFileInfos().stream()
                                                .anyMatch(file -> file.getFileName().toLowerCase().contains(lowerKey));
                    boolean urlMatches = doc.getUrlBeanList().stream()
                            .anyMatch(url -> !"".equals(url.getId()) && url.getName().toLowerCase().contains(lowerKey));
                    return nameMatches || fileMatches || urlMatches;
                })
                .collect(Collectors.toList());
    }
    /* Ended by AICoder, pid:38845mf08aza46214c1608a8b030b52422640bce */
    /* Started by AICoder, pid:t5f0an95e6rb8a51419309b3903f4f444aa0e9e3 */
    public List<DocumentInfoVo> searchByAccurate(List<DocumentInfoVo> documents, String name, String attachmentName){
        if (!name.trim().isEmpty()) {
            String lowerKey = name.toLowerCase();
            documents = documents.stream().filter(item -> item.getName().toLowerCase().contains(lowerKey)).collect(Collectors.toList());
        }
        if(!attachmentName.trim().isEmpty()){
            String lowerKey = attachmentName.toLowerCase();
            documents = documents.stream()
                        .filter(doc -> {
                            // 检查文件列表中是否有文件名匹配
                            boolean fileMatches = doc.getFileInfos().stream()
                                    .anyMatch(file -> file.getFileName().toLowerCase().contains(lowerKey));
                            boolean urlMatches = doc.getUrlBeanList().stream()
                                    .anyMatch(url -> !"".equals(url.getId()) && url.getName().toLowerCase().contains(lowerKey));
                            return fileMatches || urlMatches;
                        })
                        .collect(Collectors.toList());
        }
        return documents;
    }
    /* Ended by AICoder, pid:t5f0an95e6rb8a51419309b3903f4f444aa0e9e3 */
    /**
     * 根据ID查询文档详细信息。
     * @param id 文档ID
     * @return 文档详细信息对象
     */
    @Override
    public DocumentInfoDetailVo queryDetail(String id) {
        DocumentInfoDetailPo detailPo = documentInfoMapper.queryDetail(id);
        if (detailPo == null ) {
            return null;
        }
        DocumentInfoDetailVo detailVo = DocumentInfoConverter.INSTANCE.convert2Vo(detailPo);
        List<FileInfoVo> fileInfoVoList = documentInfoMapper.selectFileInfosById(id);
        detailVo.setFileInfos(fileInfoVoList);
        return detailVo;
    }

    /**
     * 查询所有文件ID。
     * @return 文件ID列表
     */
    @Override
    public List<String> queryAllFileIds() {
        return documentInfoMapper.queryAllFileIds();
    }

    @Override
    public int selectCitedCountById(String id) {
        return documentInfoMapper.selectCitedCountById(id);
    }

    @Override
    public List<DocumentRelationObj> querRelationById(String id) {
        return documentInfoMapper.queryRelationById(id);
    }

    @Override
    public PageVO<DocumentSearchVo> advancedSearch(DocumentAdvancedSearchDto dto) {
        Integer pageNum = dto.getPageNum();
        Integer pageSize = dto.getPageSize();
        if (pageNum == null || pageSize == null) {
            pageNum = 1;
            pageSize = 10;
        }
        //Page<String> page = PageHelper.startPage(pageNum, pageSize);
        List<String> ids = documentInfoMapper.advancedSearchIds(dto);
        if (CollectionUtils.isEmpty(ids)) {
            return new PageVO<>();
        }
        List<DocumentSearchVo> list = documentInfoMapper.advancedSearch(ids);
        log.info("list={}",list);
        for (DocumentSearchVo searchVo : list) {
            List<String> urlList = JSON.parseArray(searchVo.getUrls(), String.class);
            searchVo.setUrlList(urlList);
            ////增加显示
            searchVo.setUrlBeanList(getUrlBeansOptimized(urlList));
            List<FileInfoVo> fileInfoVoList = documentInfoMapper.selectFileInfosById(searchVo.getId());
            searchVo.setFileInfos(fileInfoVoList);
        }
        /* Started by AICoder, pid:ke65e2887f7dfa714a79090df081f84dac11d7cd */
        if(!dto.getAttachmentName().trim().isEmpty()){
            String lowerKey = dto.getAttachmentName().toLowerCase();
            list = list.stream()
                    .filter(doc -> {
                        // 检查文件列表中是否有文件名匹配
                        boolean fileMatches = doc.getFileInfos().stream()
                                .anyMatch(file -> file.getFileName().toLowerCase().contains(lowerKey));
                        boolean urlMatches = doc.getUrlBeanList().stream()
                                .anyMatch(url -> !"".equals(url.getId()) && url.getName().toLowerCase().contains(lowerKey));
                        return fileMatches || urlMatches;
                    })
                    .collect(Collectors.toList());
        }
        int total = list.size();
        int fromIndex = (pageNum - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);
        List<DocumentSearchVo> pageList = list.subList(fromIndex, toIndex);
        return new PageVO<>(list.size(), pageList);
        /* Ended by AICoder, pid:ke65e2887f7dfa714a79090df081f84dac11d7cd */
    }

    @Override
    public PageVO<DocumentSearchVo> commonSearch(DocumentCommonSearchDto dto) {
        Integer pageNum = dto.getPageNum();
        Integer pageSize = dto.getPageSize();
        if (pageNum == null || pageSize == null) {
            pageNum = 1;
            pageSize = 10;
        }
        //Page<DocumentSearchVo> page = PageHelper.startPage(pageNum, pageSize);
        List<DocumentSearchVo> list = documentInfoMapper.commonSearch(dto);
        if (CollectionUtils.isNotEmpty(list)) {
            for (DocumentSearchVo searchVo : list) {
                List<String> urlList = JSON.parseArray(searchVo.getUrls(), String.class);
                searchVo.setUrlList(urlList);
                ////增加显示
                searchVo.setUrlBeanList(getUrlBeansOptimized(urlList));
                List<FileInfoVo> fileInfoVoList = documentInfoMapper.selectFileInfosById(searchVo.getId());
                searchVo.setFileInfos(fileInfoVoList);
            }
        }
        list = searchByFuzzyName(list, dto.getCondition());
        int total = list.size();
        int fromIndex = (pageNum - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);
        List<DocumentSearchVo> pageList = list.subList(fromIndex, toIndex);
        PageVO<DocumentSearchVo> pageVO = new PageVO<>(list.size(), pageList);
        PageHelper.clearPage();
        return pageVO;
    }
    /* Started by AICoder, pid:ee39cr6db92201a14d2f0b93b0b54b3317a5c1c5 */
    public List<DocumentSearchVo> searchByFuzzyName(List<DocumentSearchVo> documents, String key){
        if (key == null || key.trim().isEmpty()) {
            return documents; // 如果key为空，返回所有数据
        }
        String lowerKey = key.toLowerCase(); // 转换为小写用于不区分大小写的匹配
        return documents.stream()
                .filter(doc -> {
                    // 检查文档名称是否匹配
                    boolean nameMatches = doc.getName().toLowerCase().contains(lowerKey) || doc.getDocumentCategoryName().toLowerCase().contains(lowerKey);
                    // 检查文件列表中是否有文件名匹配
                    boolean fileMatches = doc.getFileInfos().stream()
                            .anyMatch(file -> file.getFileName().toLowerCase().contains(lowerKey));
                    boolean urlMatches = doc.getUrlBeanList().stream()
                            .anyMatch(url -> !"".equals(url.getId()) && url.getName().toLowerCase().contains(lowerKey));
                    return nameMatches || fileMatches || urlMatches;
                })
                .collect(Collectors.toList());
    }
    /* Ended by AICoder, pid:ee39cr6db92201a14d2f0b93b0b54b3317a5c1c5 */
    /* Started by AICoder, pid:r0501t215198ea0146b90b7b00d2312cebe7ffa9 */
    @Override
    public void relateResources(List<DocumentRelationObj> relations) {
        if (relations == null || relations.isEmpty()) {
            return; // 避免传递空列表给数据库操作
        }
        documentInfoMapper.batchRelateResources(relations);
    }

    @Override
    public void deleteRelationsByResourceId(String resourceId) {
        documentInfoMapper.deleteRelationsByResourceId(resourceId);
    }

    @Override
    public List<DocumentInfoVo> queryByResourceId(String resourceId) {
        List<DocumentInfoVo> list = documentInfoMapper.queryByResourceId(resourceId);
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(vo->{
                List<String> urlList = JSON.parseArray(vo.getUrls(), String.class);
                vo.setUrlList(urlList);
                ////增加显示
                vo.setUrlBeanList(getUrlBeansOptimized(urlList));
            });
        }
        return list;
    }

    private List<IdNameBean> getUrlBeansOptimized(List<String> urlList) {
        // 检查urlList是否为空或null
        if (CollectionUtils.isEmpty(urlList)) {
            return Collections.emptyList();
        }
        // 使用IntStream生成索引流，结合mapToObj创建IdNameBean对象
        return IntStream.range(0, urlList.size())
                .mapToObj(i -> {
                    String url = urlList.get(i);
                    return new IdNameBean(url, GlobalConstants.CONNECT + (i + 1));
                })
                .collect(Collectors.toList());
    }
    @Override
    public List<DocumentInfoVo> queryByIds(List<String> ids) {
        List<DocumentInfoVo> list = documentInfoMapper.queryByIds(ids);
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(vo-> {
                List<String> urlList = JSON.parseArray(vo.getUrls(), String.class);
                vo.setUrlList(urlList);
                ////增加显示
                vo.setUrlBeanList(getUrlBeansOptimized(urlList));
            });

        }
        return list;
    }

    /* Started by AICoder, pid:m481fce6e86e12714b3b09d680c5140b03e941f8 */
    @Override
    public Boolean existRelatedDocumentByAscriptionId(String ascriptionId) {
        LambdaQueryWrapper<DocumentInfoPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DocumentInfoPo::getAscriptionId,ascriptionId);
        return documentInfoMapper.exists(queryWrapper);
    }
    /* Ended by AICoder, pid:m481fce6e86e12714b3b09d680c5140b03e941f8 */
    /* Ended by AICoder, pid:r0501t215198ea0146b90b7b00d2312cebe7ffa9 */

    /* Started by AICoder, pid:id64cmcb89e3463148680b47701143196b465287 */
    @Override
    public List<DocumentInfoVo> queryByAscriptionId(String id,String documentCategoryId) {
        List<DocumentInfoVo> list = documentInfoMapper.queryByAscriptionId(id,documentCategoryId);
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(vo -> {
                List<String> urlList = JSON.parseArray(vo.getUrls(), String.class);
                vo.setUrlList(urlList);
                ////增加显示
                vo.setUrlBeanList(getUrlBeansOptimized(urlList));
            });
        }
        return list;
    }

    @Override
    public void batchAddDocumentByIds(List<String> ids, String userId) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        LambdaQueryWrapper<DocumentInfoTempPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DocumentInfoTempPo::getCreateBy,userId);
        queryWrapper.in(DocumentInfoTempPo::getId,ids);
        List<DocumentInfoTempPo> poList = documentInfoTempMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(poList)) {
            return;
        }
        //先删后增
        documentInfoMapper.deleteBatchIds(ids);
        documentInfoMapper.batchInsert(poList);
    }
    /* Started by AICoder, pid:ed2cbq13d2c92d414b720854f04ac3000ac7459a */
    @Override
    public void deleteDocuments(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        documentInfoMapper.deleteBatchIds(ids);
    }
    /* Started by AICoder, pid:xfcc270265739a714e3c0aeb80f1a02250f1b0a7 */
    @Override
    public Set<String> queryByCondition(String projectId, String documentCategoryId, String queryTime) {
        LambdaQueryWrapper<DocumentInfoPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DocumentInfoPo::getAscriptionId,projectId)
                .eq(DocumentInfoPo::getDocumentCategoryId,documentCategoryId)
                .le(DocumentInfoPo::getCreateTime,queryTime);
        List<DocumentInfoPo> documentInfoPos = documentInfoMapper.selectList(queryWrapper);
        return documentInfoPos.stream().map(DocumentInfoPo::getId).collect(Collectors.toSet());
    }
    /* Ended by AICoder, pid:xfcc270265739a714e3c0aeb80f1a02250f1b0a7 */

    /* Started by AICoder, pid:60960s0d97760701442b0819e06b6c13c929592e */
    @Override
    public void deleteRelationsById(String documentId) {
        documentInfoMapper.deleteRelationsById(documentId);
    }

    @Override
    public void updateBrandIdsById(String id, String brandIds) {
        documentInfoMapper.updateBrandIdsById(id, brandIds);
    }

    /* Started by AICoder, pid:ye09ad34eab01c414afa081e20c05e1562c085b5 */
    @Override
    public List<DocumentInfoVo> queryDocumentByBrandId(String brandId) {
        LambdaQueryWrapper<DocumentInfoPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(DocumentInfoPo::getBrandIds, brandId);

        List<DocumentInfoPo> documentInfoPos = documentInfoMapper.selectList(queryWrapper);
        List<DocumentInfoVo> documentInfoVos = DocumentInfoConverter.INSTANCE.DocumentInfoPoListconvertDocumentInfoVoList(documentInfoPos);

        return documentInfoVos;
    }

    /* Ended by AICoder, pid:ye09ad34eab01c414afa081e20c05e1562c085b5 */
    /* Ended by AICoder, pid:60960s0d97760701442b0819e06b6c13c929592e */
    /* Started by AICoder, pid:wd755y6ca8o9d71148720b3ee091451a33c8b86c */
    @Override
    public List<DocumentInfoVo> getBidAnalysisContent(String ascriptionId, String brandId) {
        /**
         * 获取标书分析内容。
         *
         * @param ascriptionId 归属ID
         * @param brandId      品牌ID
         * @return 包含标书分析内容的 DocumentInfoVo 对象列表
         */
        List<DocumentInfoVo> list = documentInfoMapper.getBidAnalysisContent(ascriptionId, brandId);
        for (DocumentInfoVo documentInfoVo : list) {
            // 根据文档信息ID查询文件信息列表
            List<FileInfoVo> fileInfoVoList = documentInfoMapper.selectFileInfosById(documentInfoVo.getId());
            // 将文件信息列表设置到文档信息对象中
            documentInfoVo.setFileInfos(fileInfoVoList);
        }
        return list;
    }

    @Override
    public List<FileInfoVo> selectFileInfosById(String id) {
        List<FileInfoVo> fileInfoVos = documentInfoMapper.selectFileInfosById(id);
        if (CollectionUtils.isEmpty(fileInfoVos)) {
            return Collections.emptyList();
        }
        return fileInfoVos;
    }

    @Override
    public Integer getNumByCreateTimeAndAscriptionId(String createTime, String ascriptionId) {
        Integer num = documentInfoMapper.getNumByCreateTimeAndAscriptionId(createTime, ascriptionId);
        if(num == null){
            return 0;
        }
        return num;
    }
    @Override
    public Integer getNumByTimeAndNotInIds(String createTime, List<String> ids) {
        Integer num = documentInfoMapper.getNumByTimeAndNotInIds(createTime, ids);
        if(num == null) {
            return 0;
        }
        return num;
    }
    /* Ended by AICoder, pid:wd755y6ca8o9d71148720b3ee091451a33c8b86c */
}

/* Ended by AICoder, pid:11f040ea70lfb96141b30935a15daf30424821b8 */