<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.DocumentInfoMapper">
    <!-- Started by AICoder, pid:c83a4wb75ccde8f14bd30bb1802b362469f030ec -->
    <insert id="addFileIds">
        <!-- 使用单个插入语句，确保每个值对独立 -->
        insert into document_info_file (document_info_id, file_id)
        values
        <foreach collection="fileIds" item="fileId" separator=",">
            (#{id}, #{fileId})
        </foreach>
    </insert>
    <!-- Started by AICoder, pid:yf1e4da9d4p35ae144820b33a0adfb13a4b04d06 -->
    <insert id="batchRelateResources">
        insert into document_relation (
        id, document_info_id, resource_id, resource_type)
        values
        <foreach collection="list" item="bean" separator=",">
            (#{bean.id}, #{bean.documentInfoId}, #{bean.resourceId}, #{bean.resourceType})
        </foreach>
    </insert>
    <!-- Ended by AICoder, pid:yf1e4da9d4p35ae144820b33a0adfb13a4b04d06 -->

    <!-- Started by AICoder, pid:kf3b4d98c1179a5140170a62407def1450f3b74a -->
    <insert id="batchInsert">
        insert into document_info (
        id, name, ascription_id, type, document_category_id, attachment_type, urls, description,
        create_time, update_time, create_by, update_by
        ) VALUES
        <foreach collection="list" item="po" separator=",">
            (
            #{po.id}, #{po.name}, #{po.ascriptionId}, #{po.type}, #{po.documentCategoryId},
            #{po.attachmentType}, #{po.urls}, #{po.description},
            #{po.createTime}, #{po.updateTime}, #{po.createBy}, #{po.updateBy}
            )
        </foreach>
    </insert>

    <!-- Started by AICoder, pid:k67ccy20aco720814034082fb07aa603f795bd06 -->
    <update id="updateBrandIdsById">
        update document_info set brand_ids = #{brandIds} where id = #{id}
    </update>
    <!-- Ended by AICoder, pid:k67ccy20aco720814034082fb07aa603f795bd06 -->

    <!-- Ended by AICoder, pid:kf3b4d98c1179a5140170a62407def1450f3b74a -->

    <delete id="deleteByDocumentId">
        <!-- 删除指定 document_info_id 的所有记录 -->
        delete from document_info_file where document_info_id = #{id}
    </delete>
    <delete id="deleteRelationsByResourceId">
        delete from document_relation where resource_id = #{resourceId}
    </delete>

    <!-- Started by AICoder, pid:61c3cn082eva555146510850007f760db3d4dc7c -->
    <delete id="deleteRelationsById">
        delete from document_relation where document_info_id = #{documentId}
    </delete>
    <!-- Ended by AICoder, pid:61c3cn082eva555146510850007f760db3d4dc7c -->

    <select id="selectByIdAndName" resultType="java.lang.Integer">
        <!-- 查询具有相同名称但不同 ID 的记录数 -->
        select count(*) as count from document_info where name = #{name}
        <if test="id != null and id != ''">
            and id != #{id}
        </if>
    </select>

    <!-- Ended by AICoder, pid:c83a4wb75ccde8f14bd30bb1802b362469f030ec -->

    <!-- Started by AICoder, pid:de5c1ie6079cb6d1434809c400b6e479c2120438 -->
    <resultMap id="DocumentInfoResultMap" type="com.zte.uedm.dcdigital.common.bean.document.DocumentInfoVo">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="document_category_id" property="documentCategoryId"/>
        <result column="document_category_name" property="documentCategoryName"/>
        <result column="attachment_type" property="attachmentType"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="urls" property="urls"/>
        <result column="brand_type" property="brandType"/>
        <result column="brand_ids" property="brandIds"/>
        <!-- FileInfos -->
        <collection property="fileInfos" ofType="com.zte.uedm.dcdigital.common.bean.document.FileInfoVo">
            <id column="file_id" property="fileId"/>
            <result column="file_name" property="fileName"/>
        </collection>
    </resultMap>

    <!-- Started by AICoder, pid:8343c8a8d6q9ed2142c50acfd014e95f6885ba45 -->
    <select id="queryByCondition" resultType="com.zte.uedm.dcdigital.common.bean.document.DocumentInfoVo">
        SELECT
        di.id,
        di.name,
        di.document_category_id,
        dc.name AS document_category_name,
        di.attachment_type,
        di.create_time,
        di.update_time,
        di.urls,
        di.brand_type,
        di.brand_ids
        FROM document_info di
        LEFT JOIN document_category dc ON di.document_category_id = dc.id
        WHERE 1=1
<!--        <if test="name != null and name != ''">-->
<!--            AND LOWER(di.name) LIKE CONCAT('%', LOWER(#{name}), '%')-->
<!--        </if>-->
        <if test="ascriptionId != null and ascriptionId != ''">
            AND di.ascription_id = #{ascriptionId}
        </if>
        <if test="documentCategoryIds != null and documentCategoryIds.size() > 0">
            AND di.document_category_id IN (
            <foreach collection="documentCategoryIds" item="documentCategoryId" separator=",">
                #{documentCategoryId}
            </foreach>
            )
        </if>
        <if test="startTime != null and startTime != ''">
            AND di.update_time &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND di.update_time &lt;= #{endTime}
        </if>
        <if test="sort !=null and sort !=''">
            order by ${sort} ${order}
        </if>

    </select>

    <!-- Ended by AICoder, pid:8343c8a8d6q9ed2142c50acfd014e95f6885ba45 -->
    <!-- Ended by AICoder, pid:de5c1ie6079cb6d1434809c400b6e479c2120438 -->

    <!-- Started by AICoder, pid:70bbci781dt4aaa141f70a3b90ba4d3a57c9fbad -->
    <select id="queryDetail" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.DocumentInfoDetailPo">
        SELECT
        di.id,
        di.name,
        di.type,
        di.ascription_id,
        di.document_category_id,
        dc.name AS documentCategoryName,
        di.attachment_type,
        di.description,
        di.urls,
        di.brand_type,
        di.brand_ids
        FROM document_info di
        LEFT JOIN document_category dc ON di.document_category_id = dc.id
        WHERE di.id = #{id}
    </select>
    <!-- Ended by AICoder, pid:70bbci781dt4aaa141f70a3b90ba4d3a57c9fbad -->
    <!-- Started by AICoder, pid:s4e7941ab89ab0b14fc40a33509e7f0f27a3047d -->
    <select id="queryAllFileIds" resultType="java.lang.String">
        SELECT DISTINCT file_id FROM document_info_file
    </select>
    <!-- Ended by AICoder, pid:s4e7941ab89ab0b14fc40a33509e7f0f27a3047d -->
    <select id="selectCitedCountById" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM document_relation WHERE document_info_id = #{id}
    </select>
    <select id="queryRelationById"
            resultType="com.zte.uedm.dcdigital.domain.common.valueobj.DocumentRelationObj">
        SELECT * FROM document_relation WHERE document_info_id = #{id}
    </select>
    <!-- Started by AICoder, pid:606a5z27a8a48a614685099c00038340e1d59fd5 -->
    <select id="advancedSearch" resultType="com.zte.uedm.dcdigital.interfaces.web.vo.DocumentSearchVo">
        <!-- Started by AICoder, pid:s6c81q51ac51a5614a1d08390028172856c40e59 -->
        SELECT
        di.id,
        di.name,
        di.type,
        di.document_category_id,
        dc.name AS documentCategoryName,
        di.ascription_id,
        di.attachment_type,
        di.urls,
        di.update_time
        FROM
        document_info di
        LEFT JOIN
        document_category dc ON di.document_category_id = dc.id
        <where>
            <if test="idList != null and idList.size() > 0">
                AND di.id IN
                <foreach collection="idList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY
        di.update_time DESC
        <!-- Ended by AICoder, pid:s6c81q51ac51a5614a1d08390028172856c40e59 -->
    </select>

    <!-- Ended by AICoder, pid:606a5z27a8a48a614685099c00038340e1d59fd5 -->
    <!-- Started by AICoder, pid:7a69ds33e7z938e14b2008a5f0cee242469365f3 -->
    <select id="commonSearch" resultType="com.zte.uedm.dcdigital.interfaces.web.vo.DocumentSearchVo">
        select
            di.id,
            di.name,
            di.type,
            di.document_category_id,
            dc.name documentCategoryName,
            di.ascription_id,
            di.attachment_type,
            di.urls,
            di.update_time
            from
            document_info di
        left join
            document_category dc on
            di.document_category_id = dc.id
        <where>
            <if test="searchDto.type != null">
                and di.type = #{searchDto.type}
            </if>
<!--            <if test="searchDto.condition != null and searchDto.condition != ''">-->
<!--                AND (LOWER(di.name) LIKE CONCAT('%', LOWER(#{searchDto.condition}), '%')-->
<!--                OR-->
<!--                LOWER(dc.name) LIKE CONCAT('%', LOWER(#{searchDto.condition}), '%'))-->
<!--            </if>-->
        </where>
        ORDER BY di.update_time DESC
    </select>
    <!-- Started by AICoder, pid:e16e26c95dp2fa014590081bd0b8873a852024ac -->
    <select id="queryByResourceId" resultMap="DocumentInfoResultMap">
        WITH DocumentCategory AS (
        SELECT id, name AS document_category_name FROM document_category
        ),
        FileInfo AS (
        SELECT id, file_name FROM file_info
        ),
        DocumentFileInfo AS (
        SELECT df.document_info_id, f.id AS file_id, f.file_name
        FROM document_info_file df
        JOIN FileInfo f ON df.file_id = f.id
        )
        SELECT
        di.id,
        di.name,
        di.document_category_id,
        dc.document_category_name,
        di.attachment_type,
        di.create_time,
        di.update_time,
        di.urls,
        di.brand_type,
        di.brand_ids,
        df.file_id,
        df.file_name
        FROM
        document_relation dr
        LEFT JOIN document_info di ON dr.document_info_id = di.id
        LEFT JOIN DocumentCategory dc ON di.document_category_id = dc.id
        LEFT JOIN DocumentFileInfo df ON di.id = df.document_info_id
        WHERE dr.resource_id = #{resourceId}
        GROUP BY di.id, dc.document_category_name, df.file_id, df.file_name
        ORDER BY dc.document_category_name ASC, di.name ASC
    </select>
    <select id="selectFileInfosById" resultType="com.zte.uedm.dcdigital.common.bean.document.FileInfoVo">
        select fi.id as fileId, fi.file_name as fileName, fi.file_path as filePath from document_info_file dif left join file_info fi on dif.file_id = fi.id
        where dif.document_info_id = #{id}
    </select>
    <select id="advancedSearchIds" resultType="java.lang.String">
        SELECT DISTINCT di.id
        FROM document_info di
        LEFT JOIN document_category dc ON di.document_category_id = dc.id
        LEFT JOIN document_relation dr ON di.id = dr.document_info_id
        <where>
            <if test="name != null and name != ''">
                AND LOWER(di.name) LIKE CONCAT('%', LOWER(#{name}), '%')
            </if>
            <if test="type != null">
                AND di.type = #{type}
            </if>
            <if test="documentCategoryIds != null and documentCategoryIds.size() > 0">
                AND di.document_category_id IN
                <foreach item="item" collection="documentCategoryIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="productCategoryIds != null and productCategoryIds.size() > 0">
                AND di.ascription_id IN
                <foreach item="item" collection="productCategoryIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="projectIds != null and projectIds.size() > 0">
                AND dr.resource_id IN
                <foreach item="item" collection="projectIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="queryByIds" resultMap="DocumentInfoResultMap">
        SELECT
            di.id,
            di.name,
            di.document_category_id,
            dc.name as document_category_name,
            di.attachment_type,
            di.create_time,
            di.update_time,
            di.urls,
            di.brand_type,
            di.brand_ids,
            f.id,
            f.file_name
        FROM
            document_info di
        LEFT JOIN
            document_category dc ON di.document_category_id = dc.id
        LEFT JOIN document_info_file df ON di.id = df.document_info_id
        LEFT JOIN file_info f ON df.file_id = f.id
        WHERE di.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY
            di.id, di.name, di.document_category_id, dc.name, di.attachment_type, di.create_time, di.update_time,
            di.urls, f.id, f.file_name
        ORDER BY dc.name asc, di.name asc
    </select>
    <select id="queryByAscriptionId" resultMap="DocumentInfoResultMap">
        WITH DocumentCategory AS (
            SELECT id, name AS document_category_name FROM document_category
        ),
             FileInfo AS (
                 SELECT id, file_name FROM file_info
             ),
             DocumentFileInfo AS (
                 SELECT df.document_info_id, f.id AS file_id, f.file_name
                 FROM document_info_file df
                          JOIN FileInfo f ON df.file_id = f.id
             )
        SELECT
            di.id,
            di.name,
            di.document_category_id,
            dc.document_category_name,
            di.attachment_type,
            di.create_time,
            di.update_time,
            di.urls,
            di.brand_type,
            di.brand_ids,
            df.file_id,
            df.file_name
        FROM
        document_info di
                LEFT JOIN DocumentCategory dc ON di.document_category_id = dc.id
                LEFT JOIN DocumentFileInfo df ON di.id = df.document_info_id
        WHERE di.ascription_id = #{id}
        <if test="documentCategoryId != null and documentCategoryId != ''">
            AND di.document_category_id = #{documentCategoryId}
        </if>
        GROUP BY di.id, dc.document_category_name, df.file_id, df.file_name
    </select>

    <!-- Ended by AICoder, pid:e16e26c95dp2fa014590081bd0b8873a852024ac -->
    <!-- Ended by AICoder, pid:7a69ds33e7z938e14b2008a5f0cee242469365f3 -->

    <select id="getBidAnalysisContent" resultType="com.zte.uedm.dcdigital.common.bean.document.DocumentInfoVo">
        SELECT
        di.id,
        di.name,
        di.document_category_id,
        dc.name AS document_category_name,
        di.attachment_type,
        di.create_time,
        di.update_time,
        di.urls,
        di.brand_type,
        di.brand_ids
        FROM document_info di
        LEFT JOIN document_category dc ON di.document_category_id = dc.id
        WHERE  di.ascription_id = #{ascriptionId}
        and  di.brand_ids  LIKE CONCAT('%', CONCAT(#{brandId}, '%'))
        and  di.attachment_type = '0'
        and  di.type = '1'

    </select>

    <select id="getNumByCreateTimeAndAscriptionId" resultType="int">
        SELECT count(*) FROM document_info where ascription_id = #{ascriptionId} AND create_time &lt; #{createTime}
    </select>

    <select id="getNumByTimeAndNotInIds" resultType="int">
        SELECT count(*) FROM document_info
        WHERE ascription_id NOT IN
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND create_time &lt; #{createTime}
    </select>
</mapper>